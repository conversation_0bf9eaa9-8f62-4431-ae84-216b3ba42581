import pandas as pd
import numpy as np
from tabulate import tabulate
import warnings
warnings.filterwarnings('ignore')

print("PART 1: RISK & RETURN METRICS CALCULATION")
print("=" * 60)

try:
    #####################################################
    # STEP 1: LOAD AND CLEAN DATA
    #####################################################
    print("\nSTEP 1: Loading and cleaning data from Sheet2...")
    
    # Load the Excel file
    file_path = "/Users/<USER>/Desktop/PythonProject1/venv/Portfolio Optimser.xlsx"
    
    # Read Sheet2
    df = pd.read_excel(file_path, sheet_name='Sheet2')
    
    # Convert the first column to datetime and set as index
    df['date'] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    
    # Sort data chronologically (oldest first)
    df = df.sort_index()
    
    print(f"Data range: {df.index.min()} to {df.index.max()}")
    print(f"Number of days: {len(df)}")
    print(f"Number of funds: {len(df.columns)}")
    
    # Fill any missing values
    df_filled = df.ffill().bfill()
    
    # Calculate daily returns
    returns = df_filled.pct_change().dropna()
    
    # Fill any NaN values
    returns_clean = returns.ffill().bfill()
    
    print(f"\nClean returns data shape: {returns_clean.shape}")
    
    #####################################################
    # STEP 2: CATEGORIZE FUNDS
    #####################################################
    print("\nSTEP 2: Categorizing funds...")
    
    # Define fund categories
    fund_categories = {}
    
    # Index Funds
    index_funds = [
        'Nippon India Index Fund – Sensex Plan',
        'UTI Nifty 50 Index Fund',
        'HDFC Index Fund – Nifty 50 Plan',
        'ICICI Prudential Nifty Next 50 Index',
        'Axis Bluechip Fund'
    ]
    
    # Mid Cap Funds
    mid_cap_funds = [
        'Motilal Oswal Midcap Fund',
        'Edelweiss Mid Cap Fund',
        'Nippon India Growth Fund',
        'Quant Mid Cap Fund',
        'Invesco India Mid Cap Fund'
    ]
    
    # Small Cap Funds
    small_cap_funds = [
        'Bandhan Small Cap Fund',
        'Quant Small Cap Fund',
        'Nippon India Small Cap Fund',
        'Invesco India Smallcap Fund',
        'Bank of India Small Cap Fund'
    ]
    
    # Flexi Cap Funds
    flexi_cap_funds = [
        'Parag Parikh Flexi Cap Fund',
        'HDFC Flexi Cap Fund',
        'Quant Flexi Cap Fund',
        'Motilal Oswal Flexi Cap'
    ]
    
    # Debt Funds
    debt_funds = [
        'SBI Magnum Gilt Fund – Direct Growth',
        'Nippon India Ultra Short Duration Fund – Direct Growth',
        'Edelweiss Liquid Fund – Direct Growth',
        'Axis Corporate Bond Fund – Direct Growth'
    ]
    
    # Commodity Funds
    commodity_funds = [
        'Nippon India ETF Gold BeES',
        'Nippon India Silver ETF'
    ]
    
    # Create category mapping
    for fund in index_funds:
        if fund in df.columns:
            fund_categories[fund] = 'Index'
    
    for fund in mid_cap_funds:
        if fund in df.columns:
            fund_categories[fund] = 'Mid Cap'
    
    for fund in small_cap_funds:
        if fund in df.columns:
            fund_categories[fund] = 'Small Cap'
    
    for fund in flexi_cap_funds:
        if fund in df.columns:
            fund_categories[fund] = 'Flexi Cap'
    
    for fund in debt_funds:
        if fund in df.columns:
            fund_categories[fund] = 'Debt'
    
    for fund in commodity_funds:
        if fund in df.columns:
            fund_categories[fund] = 'Commodity'
    
    print(f"Categorized {len(fund_categories)} funds")
    
    #####################################################
    # STEP 3: CALCULATE METRICS FOR EACH FUND
    #####################################################
    print("\nSTEP 3: Calculating risk and return metrics...")
    
    def calculate_cagr(nav_series, years):
        """Calculate CAGR for a given number of years"""
        if len(nav_series) < years * 252:  # Not enough data
            return np.nan
        
        start_value = nav_series.iloc[-(years * 252)]
        end_value = nav_series.iloc[-1]
        
        cagr = (end_value / start_value) ** (1 / years) - 1
        return cagr
    
    def calculate_rolling_returns(returns_series, years):
        """Calculate rolling returns for a given period"""
        if len(returns_series) < years * 252:  # Not enough data
            return np.nan
        
        # Calculate rolling returns
        rolling_returns = returns_series.rolling(window=years * 252).apply(
            lambda x: (1 + x).prod() - 1
        )
        
        return rolling_returns.dropna()
    
    def calculate_max_drawdown(nav_series):
        """Calculate maximum drawdown"""
        cumulative = nav_series / nav_series.iloc[0]  # Normalize to start at 1
        running_max = cumulative.cummax()
        drawdown = (cumulative / running_max) - 1
        return drawdown.min()
    
    def calculate_sharpe_ratio(returns_series, risk_free_rate=0.06):
        """Calculate Sharpe ratio with 6% risk-free rate"""
        excess_returns = returns_series.mean() * 252 - risk_free_rate
        volatility = returns_series.std() * np.sqrt(252)
        
        if volatility == 0:
            return 0
        
        return excess_returns / volatility
    
    # Initialize results list
    fund_metrics = []
    
    # Calculate metrics for each fund
    for fund in df.columns:
        print(f"Processing: {fund}")
        
        nav_series = df_filled[fund]
        returns_series = returns_clean[fund]
        
        # Basic metrics
        daily_std = returns_series.std()
        annual_std = daily_std * np.sqrt(252)
        
        # CAGR calculations
        cagr_1y = calculate_cagr(nav_series, 1)
        cagr_3y = calculate_cagr(nav_series, 3)
        cagr_5y = calculate_cagr(nav_series, 5)
        
        # Rolling returns (get the latest value)
        rolling_1y = calculate_rolling_returns(returns_series, 1)
        rolling_3y = calculate_rolling_returns(returns_series, 3)
        rolling_5y = calculate_rolling_returns(returns_series, 5)
        
        latest_rolling_1y = rolling_1y.iloc[-1] if len(rolling_1y) > 0 else np.nan
        latest_rolling_3y = rolling_3y.iloc[-1] if len(rolling_3y) > 0 else np.nan
        latest_rolling_5y = rolling_5y.iloc[-1] if len(rolling_5y) > 0 else np.nan
        
        # Sharpe ratio
        sharpe = calculate_sharpe_ratio(returns_series)
        
        # Maximum drawdown
        max_dd = calculate_max_drawdown(nav_series)
        
        # Get category
        category = fund_categories.get(fund, 'Other')
        
        # Store results
        fund_metrics.append({
            'Fund Name': fund,
            'Category': category,
            '1Y CAGR': cagr_1y,
            '3Y CAGR': cagr_3y,
            '5Y CAGR': cagr_5y,
            '1Y Rolling Return': latest_rolling_1y,
            '3Y Rolling Return': latest_rolling_3y,
            '5Y Rolling Return': latest_rolling_5y,
            'Daily StDev': daily_std,
            'Ann. StDev': annual_std,
            'Sharpe Ratio': sharpe,
            'Max Drawdown': max_dd
        })
    
    # Convert to DataFrame
    metrics_df = pd.DataFrame(fund_metrics)
    
    # Sort by category and then by Sharpe ratio
    metrics_df = metrics_df.sort_values(['Category', 'Sharpe Ratio'], ascending=[True, False])
    
    #####################################################
    # STEP 4: DISPLAY RESULTS
    #####################################################
    print("\nSTEP 4: Displaying results...")
    
    # Create a formatted version for display
    display_df = metrics_df.copy()
    
    # Format percentage columns
    percentage_cols = ['1Y CAGR', '3Y CAGR', '5Y CAGR', '1Y Rolling Return', 
                      '3Y Rolling Return', '5Y Rolling Return', 'Daily StDev', 
                      'Ann. StDev', 'Max Drawdown']
    
    for col in percentage_cols:
        display_df[col] = display_df[col].apply(
            lambda x: f"{x*100:.2f}%" if pd.notnull(x) else "N/A"
        )
    
    # Format Sharpe ratio
    display_df['Sharpe Ratio'] = display_df['Sharpe Ratio'].apply(
        lambda x: f"{x:.3f}" if pd.notnull(x) else "N/A"
    )
    
    # Display by category
    categories = display_df['Category'].unique()
    
    for category in categories:
        print(f"\n{category.upper()} FUNDS:")
        print("=" * 80)
        
        category_funds = display_df[display_df['Category'] == category]
        
        # Select key columns for display
        display_cols = ['Fund Name', '1Y CAGR', '3Y CAGR', '5Y CAGR', 
                       'Ann. StDev', 'Sharpe Ratio', 'Max Drawdown']
        
        print(tabulate(category_funds[display_cols], 
                      headers='keys', 
                      tablefmt='grid', 
                      showindex=False))
    
    # Summary statistics by category
    print("\n\nSUMMARY BY CATEGORY:")
    print("=" * 50)
    
    summary_stats = []
    for category in categories:
        cat_data = metrics_df[metrics_df['Category'] == category]
        
        summary_stats.append({
            'Category': category,
            'Count': len(cat_data),
            'Avg 1Y CAGR': cat_data['1Y CAGR'].mean(),
            'Avg 3Y CAGR': cat_data['3Y CAGR'].mean(),
            'Avg 5Y CAGR': cat_data['5Y CAGR'].mean(),
            'Avg Ann. StDev': cat_data['Ann. StDev'].mean(),
            'Avg Sharpe': cat_data['Sharpe Ratio'].mean(),
            'Avg Max DD': cat_data['Max Drawdown'].mean()
        })
    
    summary_df = pd.DataFrame(summary_stats)
    
    # Format summary for display
    summary_display = summary_df.copy()
    for col in ['Avg 1Y CAGR', 'Avg 3Y CAGR', 'Avg 5Y CAGR', 'Avg Ann. StDev', 'Avg Max DD']:
        summary_display[col] = summary_display[col].apply(
            lambda x: f"{x*100:.2f}%" if pd.notnull(x) else "N/A"
        )
    
    summary_display['Avg Sharpe'] = summary_display['Avg Sharpe'].apply(
        lambda x: f"{x:.3f}" if pd.notnull(x) else "N/A"
    )
    
    print(tabulate(summary_display, headers='keys', tablefmt='grid', showindex=False))
    
    #####################################################
    # STEP 5: SAVE RESULTS
    #####################################################
    print("\nSTEP 5: Saving results to Excel...")
    
    # Save to Excel
    excel_file = 'Part1_Fund_Metrics.xlsx'
    with pd.ExcelWriter(excel_file) as writer:
        # Save detailed metrics
        metrics_df.to_excel(writer, sheet_name='Fund_Metrics', index=False)
        
        # Save summary by category
        summary_df.to_excel(writer, sheet_name='Category_Summary', index=False)
        
        # Save funds by category
        for category in categories:
            cat_data = metrics_df[metrics_df['Category'] == category]
            sheet_name = f'{category}_Funds'
            # Replace invalid characters for sheet names
            sheet_name = sheet_name.replace(' ', '_').replace('/', '_')
            cat_data.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"Results saved to '{excel_file}'")
    
    # Identify top performers
    print("\n\nTOP PERFORMERS:")
    print("=" * 40)
    
    # Top 5 by Sharpe ratio
    top_sharpe = metrics_df.nlargest(5, 'Sharpe Ratio')
    print("\nTop 5 by Sharpe Ratio:")
    print(tabulate(top_sharpe[['Fund Name', 'Category', 'Sharpe Ratio', '1Y CAGR', 'Ann. StDev']], 
                  headers='keys', tablefmt='grid', showindex=False))
    
    # Top 5 by 1Y CAGR
    top_cagr = metrics_df.nlargest(5, '1Y CAGR')
    print("\nTop 5 by 1Y CAGR:")
    print(tabulate(top_cagr[['Fund Name', 'Category', '1Y CAGR', 'Sharpe Ratio', 'Max Drawdown']], 
                  headers='keys', tablefmt='grid', showindex=False))
    
    # Funds meeting your criteria (CAGR > 15%, Volatility < 15%, Max DD < 15%)
    criteria_funds = metrics_df[
        (metrics_df['1Y CAGR'] > 0.15) & 
        (metrics_df['Ann. StDev'] < 0.15) & 
        (metrics_df['Max Drawdown'] > -0.15)
    ]
    
    print(f"\nFunds meeting criteria (CAGR > 15%, Vol < 15%, Max DD < 15%): {len(criteria_funds)}")
    if len(criteria_funds) > 0:
        print(tabulate(criteria_funds[['Fund Name', 'Category', '1Y CAGR', 'Ann. StDev', 'Max Drawdown', 'Sharpe Ratio']], 
                      headers='keys', tablefmt='grid', showindex=False))
    else:
        print("No funds meet all three criteria simultaneously.")
    
    print("\nPart 1 Complete!")
    print("Next: Run Part 2 for Portfolio Templates")
    
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
