import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tabulate import tabulate
import warnings
warnings.filterwarnings('ignore')

print("PART 3: SIP SIMULATION FOR ALL PORTFOLIOS")
print("=" * 60)

try:
    #####################################################
    # STEP 1: LOAD PORTFOLIO TEMPLATES AND DATA
    #####################################################
    print("\nSTEP 1: Loading portfolio templates and NAV data...")
    
    # Load portfolio allocations from Part 2
    portfolio_allocations = pd.read_excel('Part2_Portfolio_Templates.xlsx', sheet_name='Portfolio_Allocations')
    
    # Load the original NAV data
    file_path = "/Users/<USER>/Desktop/PythonProject1/venv/Portfolio Optimser.xlsx"
    df = pd.read_excel(file_path, sheet_name='Sheet2')
    df['date'] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    df = df.sort_index()
    df_filled = df.ffill().bfill()
    
    print(f"NAV data range: {df.index.min()} to {df.index.max()}")
    print(f"Portfolio templates loaded: {len(portfolio_allocations)}")
    
    # Get unique portfolio combinations
    portfolios = portfolio_allocations[['Risk Profile', 'Horizon']].drop_duplicates()
    
    #####################################################
    # STEP 2: CALCULATE PORTFOLIO NAV TIME SERIES
    #####################################################
    print("\nSTEP 2: Calculating portfolio NAV time series...")
    
    def calculate_portfolio_nav(portfolio_name, horizon):
        """Calculate portfolio NAV time series based on fund weights"""
        
        # Get fund allocations for this portfolio
        portfolio_funds = portfolio_allocations[
            (portfolio_allocations['Risk Profile'] == portfolio_name) & 
            (portfolio_allocations['Horizon'] == horizon)
        ].copy()
        
        if len(portfolio_funds) == 0:
            return None
        
        # Initialize portfolio NAV series
        portfolio_nav = pd.Series(index=df.index, dtype=float)
        portfolio_nav.iloc[0] = 100  # Start with base value of 100
        
        # Calculate weighted NAV for each day
        for i in range(len(df.index)):
            daily_value = 0
            
            for _, fund_row in portfolio_funds.iterrows():
                fund_name = fund_row['Fund Name']
                weight = fund_row['Weight %'] / 100
                
                if fund_name in df.columns:
                    # Normalize fund NAV to start at 100 on first day
                    fund_nav_normalized = df[fund_name] / df[fund_name].iloc[0] * 100
                    daily_value += fund_nav_normalized.iloc[i] * weight
            
            portfolio_nav.iloc[i] = daily_value
        
        return portfolio_nav
    
    # Calculate NAV for all portfolios
    portfolio_navs = {}
    for _, row in portfolios.iterrows():
        portfolio_key = f"{row['Risk Profile']}_{row['Horizon']}"
        nav_series = calculate_portfolio_nav(row['Risk Profile'], row['Horizon'])
        if nav_series is not None:
            portfolio_navs[portfolio_key] = nav_series
    
    print(f"Portfolio NAV series calculated for {len(portfolio_navs)} portfolios")
    
    #####################################################
    # STEP 3: SIP SIMULATION FUNCTION
    #####################################################
    print("\nSTEP 3: Setting up SIP simulation...")
    
    def simulate_sip(nav_series, monthly_investment=10000, years=10):
        """Simulate SIP investment over specified years"""
        
        # Resample NAV to monthly (end of month values)
        monthly_nav = nav_series.resample('ME').last()
        
        # Calculate number of months
        total_months = years * 12
        
        if len(monthly_nav) < total_months:
            print(f"Warning: Not enough data for {years} years. Using available data ({len(monthly_nav)} months)")
            total_months = len(monthly_nav)
        
        # Initialize tracking variables
        sip_data = []
        total_invested = 0
        total_units = 0
        
        # Simulate monthly SIP
        for month in range(total_months):
            nav_value = monthly_nav.iloc[month]
            
            # Calculate units purchased this month
            units_purchased = monthly_investment / nav_value
            total_units += units_purchased
            total_invested += monthly_investment
            
            # Calculate current portfolio value
            current_value = total_units * nav_value
            
            # Calculate returns
            if total_invested > 0:
                absolute_return = current_value - total_invested
                return_percentage = (absolute_return / total_invested) * 100
                
                # Calculate annualized return
                years_elapsed = (month + 1) / 12
                if years_elapsed > 0:
                    annualized_return = ((current_value / total_invested) ** (1 / years_elapsed) - 1) * 100
                else:
                    annualized_return = 0
            else:
                absolute_return = 0
                return_percentage = 0
                annualized_return = 0
            
            # Store data
            sip_data.append({
                'Month': month + 1,
                'Year': (month // 12) + 1,
                'Date': monthly_nav.index[month],
                'NAV': nav_value,
                'Investment': monthly_investment,
                'Units Purchased': units_purchased,
                'Total Units': total_units,
                'Total Invested': total_invested,
                'Portfolio Value': current_value,
                'Absolute Return': absolute_return,
                'Return %': return_percentage,
                'Annualized Return %': annualized_return
            })
        
        return pd.DataFrame(sip_data)
    
    #####################################################
    # STEP 4: RUN SIP SIMULATIONS
    #####################################################
    print("\nSTEP 4: Running SIP simulations for all portfolios...")
    
    sip_results = {}
    sip_summary = []
    
    # Investment periods to simulate
    investment_periods = [5, 10, 15]
    
    for portfolio_key, nav_series in portfolio_navs.items():
        portfolio_name, horizon = portfolio_key.split('_')
        
        print(f"\nSimulating SIP for {portfolio_name} - {horizon}...")
        
        for years in investment_periods:
            print(f"  {years} years...")
            
            # Run SIP simulation
            sip_result = simulate_sip(nav_series, monthly_investment=10000, years=years)
            
            if len(sip_result) > 0:
                # Store detailed results
                result_key = f"{portfolio_key}_{years}Y"
                sip_results[result_key] = sip_result
                
                # Get final values
                final_row = sip_result.iloc[-1]
                
                # Add to summary
                sip_summary.append({
                    'Risk Profile': portfolio_name,
                    'Horizon': horizon,
                    'Investment Period': f"{years} Years",
                    'Total Invested': final_row['Total Invested'],
                    'Final Value': final_row['Portfolio Value'],
                    'Absolute Return': final_row['Absolute Return'],
                    'Return %': final_row['Return %'],
                    'Annualized Return %': final_row['Annualized Return %']
                })
    
    #####################################################
    # STEP 5: DISPLAY RESULTS
    #####################################################
    print("\nSTEP 5: Displaying SIP simulation results...")
    
    # Create summary DataFrame
    summary_df = pd.DataFrame(sip_summary)
    
    # Display results by portfolio
    for portfolio_key in portfolio_navs.keys():
        portfolio_name, horizon = portfolio_key.split('_')
        
        print(f"\n{portfolio_name.upper()} - {horizon.upper()} TERM SIP RESULTS:")
        print("=" * 70)
        
        # Get results for this portfolio
        portfolio_summary = summary_df[
            (summary_df['Risk Profile'] == portfolio_name) & 
            (summary_df['Horizon'] == horizon)
        ].copy()
        
        if len(portfolio_summary) > 0:
            # Format for display
            display_summary = portfolio_summary.copy()
            display_summary['Total Invested'] = display_summary['Total Invested'].apply(lambda x: f"₹{x:,.0f}")
            display_summary['Final Value'] = display_summary['Final Value'].apply(lambda x: f"₹{x:,.0f}")
            display_summary['Absolute Return'] = display_summary['Absolute Return'].apply(lambda x: f"₹{x:,.0f}")
            display_summary['Return %'] = display_summary['Return %'].apply(lambda x: f"{x:.2f}%")
            display_summary['Annualized Return %'] = display_summary['Annualized Return %'].apply(lambda x: f"{x:.2f}%")
            
            print(tabulate(display_summary[['Investment Period', 'Total Invested', 'Final Value', 
                                         'Absolute Return', 'Return %', 'Annualized Return %']], 
                          headers='keys', tablefmt='grid', showindex=False))
    
    #####################################################
    # STEP 6: CREATE VISUALIZATIONS
    #####################################################
    print("\nSTEP 6: Creating visualizations...")
    
    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Create SIP growth charts for each portfolio (10-year period)
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('SIP Growth Over Time (10 Years) - ₹10,000 Monthly Investment', fontsize=16, fontweight='bold')
    
    portfolio_keys = list(portfolio_navs.keys())
    
    for i, portfolio_key in enumerate(portfolio_keys):
        row = i // 3
        col = i % 3
        
        if row < 2 and col < 3:  # Ensure we don't exceed subplot grid
            ax = axes[row, col]
            
            # Get 10-year SIP data
            result_key = f"{portfolio_key}_10Y"
            if result_key in sip_results:
                sip_data = sip_results[result_key]
                
                # Plot invested vs portfolio value
                years = sip_data['Month'] / 12
                ax.plot(years, sip_data['Total Invested'], label='Total Invested', linewidth=2, linestyle='--')
                ax.plot(years, sip_data['Portfolio Value'], label='Portfolio Value', linewidth=2)
                
                # Fill area between lines
                ax.fill_between(years, sip_data['Total Invested'], sip_data['Portfolio Value'], 
                               alpha=0.3, where=(sip_data['Portfolio Value'] >= sip_data['Total Invested']))
                
                portfolio_name, horizon = portfolio_key.split('_')
                ax.set_title(f'{portfolio_name} - {horizon} Term', fontweight='bold')
                ax.set_xlabel('Years')
                ax.set_ylabel('Amount (₹)')
                ax.legend()
                ax.grid(True, alpha=0.3)
                
                # Format y-axis to show values in lakhs
                ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'₹{x/100000:.1f}L'))
    
    # Remove empty subplots
    for i in range(len(portfolio_keys), 6):
        row = i // 3
        col = i % 3
        if row < 2 and col < 3:
            fig.delaxes(axes[row, col])
    
    plt.tight_layout()
    plt.savefig('SIP_Growth_Charts.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create comparison chart for final values
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Prepare data for comparison
    comparison_data = summary_df[summary_df['Investment Period'] == '10 Years'].copy()
    comparison_data['Portfolio'] = comparison_data['Risk Profile'] + ' - ' + comparison_data['Horizon']
    
    # Create bar chart
    bars = ax.bar(range(len(comparison_data)), comparison_data['Final Value'], 
                  color=sns.color_palette("husl", len(comparison_data)))
    
    # Add value labels on bars
    for i, (bar, value) in enumerate(zip(bars, comparison_data['Final Value'])):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'₹{value/100000:.1f}L', ha='center', va='bottom', fontweight='bold')
    
    ax.set_title('Portfolio Values After 10 Years of SIP (₹10,000/month)', fontsize=14, fontweight='bold')
    ax.set_xlabel('Portfolio Type')
    ax.set_ylabel('Final Portfolio Value (₹)')
    ax.set_xticks(range(len(comparison_data)))
    ax.set_xticklabels(comparison_data['Portfolio'], rotation=45, ha='right')
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'₹{x/100000:.1f}L'))
    ax.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('SIP_Portfolio_Comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Charts saved: 'SIP_Growth_Charts.png' and 'SIP_Portfolio_Comparison.png'")
    
    #####################################################
    # STEP 7: SAVE RESULTS
    #####################################################
    print("\nSTEP 7: Saving SIP simulation results...")
    
    # Save to Excel
    excel_file = 'Part3_SIP_Simulation.xlsx'
    with pd.ExcelWriter(excel_file) as writer:
        # Save summary
        summary_df.to_excel(writer, sheet_name='SIP_Summary', index=False)
        
        # Save detailed results for each portfolio and period
        for result_key, sip_data in sip_results.items():
            # Create sheet name (Excel has 31 character limit)
            sheet_name = result_key.replace('_', ' ')[:31]
            sip_data.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # Create yearly summary for each portfolio
        yearly_summaries = []
        for result_key, sip_data in sip_results.items():
            portfolio_info = result_key.replace('_', ' ')
            
            # Group by year and get year-end values
            yearly_data = sip_data.groupby('Year').last().reset_index()
            yearly_data['Portfolio'] = portfolio_info
            yearly_summaries.append(yearly_data[['Portfolio', 'Year', 'Total Invested', 
                                               'Portfolio Value', 'Return %', 'Annualized Return %']])
        
        if yearly_summaries:
            yearly_summary_df = pd.concat(yearly_summaries, ignore_index=True)
            yearly_summary_df.to_excel(writer, sheet_name='Yearly_Summary', index=False)
    
    print(f"Results saved to '{excel_file}'")
    
    #####################################################
    # STEP 8: KEY INSIGHTS
    #####################################################
    print("\nSTEP 8: Key insights from SIP simulation...")
    
    # Find best performing portfolios
    best_10y = summary_df[summary_df['Investment Period'] == '10 Years'].nlargest(1, 'Annualized Return %')
    best_15y = summary_df[summary_df['Investment Period'] == '15 Years'].nlargest(1, 'Annualized Return %')
    
    print("\nKEY INSIGHTS:")
    print("=" * 40)
    
    if len(best_10y) > 0:
        best_10y_row = best_10y.iloc[0]
        print(f"\nBest 10-Year SIP Performance:")
        print(f"Portfolio: {best_10y_row['Risk Profile']} - {best_10y_row['Horizon']}")
        print(f"Final Value: ₹{best_10y_row['Final Value']:,.0f}")
        print(f"Annualized Return: {best_10y_row['Annualized Return %']:.2f}%")
    
    if len(best_15y) > 0:
        best_15y_row = best_15y.iloc[0]
        print(f"\nBest 15-Year SIP Performance:")
        print(f"Portfolio: {best_15y_row['Risk Profile']} - {best_15y_row['Horizon']}")
        print(f"Final Value: ₹{best_15y_row['Final Value']:,.0f}")
        print(f"Annualized Return: {best_15y_row['Annualized Return %']:.2f}%")
    
    # Calculate average returns by risk profile
    avg_returns = summary_df.groupby('Risk Profile')['Annualized Return %'].mean().sort_values(ascending=False)
    print(f"\nAverage Annualized Returns by Risk Profile:")
    for profile, avg_return in avg_returns.items():
        print(f"{profile}: {avg_return:.2f}%")
    
    # Show power of compounding
    print(f"\nPower of Compounding (₹10,000 monthly SIP):")
    for years in [5, 10, 15]:
        total_invested = years * 12 * 10000
        avg_final_value = summary_df[summary_df['Investment Period'] == f'{years} Years']['Final Value'].mean()
        wealth_creation = avg_final_value - total_invested
        print(f"{years} Years: Invested ₹{total_invested:,.0f} → Average Value ₹{avg_final_value:,.0f} (Wealth Created: ₹{wealth_creation:,.0f})")
    
    print("\nPart 3 Complete!")
    print("Next: Run Part 4 for Monte Carlo Simulation")
    
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
