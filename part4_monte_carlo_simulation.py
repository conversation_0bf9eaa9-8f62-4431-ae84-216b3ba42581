import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tabulate import tabulate
import warnings
warnings.filterwarnings('ignore')

print("PART 4: MONTE CARLO SIMULATION FOR ALL PORTFOLIOS")
print("=" * 70)

try:
    #####################################################
    # STEP 1: LOAD PORTFOLIO DATA AND METRICS
    #####################################################
    print("\nSTEP 1: Loading portfolio data and metrics...")
    
    # Load portfolio allocations and metrics from previous parts
    portfolio_allocations = pd.read_excel('Part2_Portfolio_Templates.xlsx', sheet_name='Portfolio_Allocations')
    portfolio_metrics = pd.read_excel('Part2_Portfolio_Templates.xlsx', sheet_name='Portfolio_Metrics')
    
    # Load the original returns data for volatility calculation
    file_path = "/Users/<USER>/Desktop/PythonProject1/venv/Portfolio Optimser.xlsx"
    df = pd.read_excel(file_path, sheet_name='Sheet2')
    df['date'] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    df = df.sort_index()
    df_filled = df.ffill().bfill()
    returns = df_filled.pct_change().dropna()
    returns_clean = returns.ffill().bfill()
    
    print(f"Portfolio metrics loaded for {len(portfolio_metrics)} portfolios")
    
    #####################################################
    # STEP 2: CALCULATE PORTFOLIO RETURN STATISTICS
    #####################################################
    print("\nSTEP 2: Calculating portfolio return statistics...")
    
    def calculate_portfolio_statistics(portfolio_name, horizon):
        """Calculate mean return and volatility for a portfolio"""
        
        # Get fund allocations for this portfolio
        portfolio_funds = portfolio_allocations[
            (portfolio_allocations['Risk Profile'] == portfolio_name) & 
            (portfolio_allocations['Horizon'] == horizon)
        ].copy()
        
        if len(portfolio_funds) == 0:
            return None, None
        
        # Calculate portfolio daily returns
        portfolio_returns = pd.Series(index=returns_clean.index, dtype=float)
        portfolio_returns[:] = 0
        
        for _, fund_row in portfolio_funds.iterrows():
            fund_name = fund_row['Fund Name']
            weight = fund_row['Weight %'] / 100
            
            if fund_name in returns_clean.columns:
                portfolio_returns += returns_clean[fund_name] * weight
        
        # Calculate statistics
        mean_daily_return = portfolio_returns.mean()
        daily_volatility = portfolio_returns.std()
        
        # Annualize
        mean_annual_return = (1 + mean_daily_return) ** 252 - 1
        annual_volatility = daily_volatility * np.sqrt(252)
        
        return mean_annual_return, annual_volatility
    
    # Calculate statistics for all portfolios
    portfolio_stats = {}
    
    for _, row in portfolio_metrics.iterrows():
        portfolio_key = f"{row['Risk Profile']}_{row['Horizon']}"
        mean_return, volatility = calculate_portfolio_statistics(row['Risk Profile'], row['Horizon'])
        
        if mean_return is not None:
            portfolio_stats[portfolio_key] = {
                'mean_return': mean_return,
                'volatility': volatility,
                'risk_profile': row['Risk Profile'],
                'horizon': row['Horizon']
            }
    
    print(f"Portfolio statistics calculated for {len(portfolio_stats)} portfolios")
    
    #####################################################
    # STEP 3: MONTE CARLO SIMULATION FUNCTION
    #####################################################
    print("\nSTEP 3: Setting up Monte Carlo simulation...")
    
    def monte_carlo_simulation(mean_return, volatility, initial_investment=100000, 
                             monthly_sip=10000, years=10, num_simulations=10000):
        """
        Run Monte Carlo simulation for portfolio returns
        
        Parameters:
        - mean_return: Expected annual return
        - volatility: Annual volatility
        - initial_investment: Initial lump sum investment
        - monthly_sip: Monthly SIP amount
        - years: Investment horizon in years
        - num_simulations: Number of simulation paths
        """
        
        # Convert to monthly parameters
        monthly_return = (1 + mean_return) ** (1/12) - 1
        monthly_volatility = volatility / np.sqrt(12)
        
        total_months = years * 12
        
        # Initialize results array
        final_values = np.zeros(num_simulations)
        all_paths = np.zeros((num_simulations, total_months + 1))
        
        for sim in range(num_simulations):
            # Initialize portfolio value
            portfolio_value = initial_investment
            all_paths[sim, 0] = portfolio_value
            
            for month in range(total_months):
                # Generate random return for this month
                random_return = np.random.normal(monthly_return, monthly_volatility)
                
                # Apply return to existing portfolio
                portfolio_value *= (1 + random_return)
                
                # Add monthly SIP
                portfolio_value += monthly_sip
                
                # Store path value
                all_paths[sim, month + 1] = portfolio_value
            
            final_values[sim] = portfolio_value
        
        return final_values, all_paths
    
    #####################################################
    # STEP 4: RUN MONTE CARLO SIMULATIONS
    #####################################################
    print("\nSTEP 4: Running Monte Carlo simulations...")
    
    # Simulation parameters
    initial_investment = 0  # Start with 0 for pure SIP
    monthly_sip = 10000
    years = 10
    num_simulations = 10000
    target_amount = 2500000  # ₹25 lakhs
    
    monte_carlo_results = {}
    simulation_summary = []
    
    for portfolio_key, stats in portfolio_stats.items():
        print(f"\nRunning Monte Carlo for {portfolio_key}...")
        
        # Run simulation
        final_values, all_paths = monte_carlo_simulation(
            mean_return=stats['mean_return'],
            volatility=stats['volatility'],
            initial_investment=initial_investment,
            monthly_sip=monthly_sip,
            years=years,
            num_simulations=num_simulations
        )
        
        # Calculate percentiles
        percentile_5 = np.percentile(final_values, 5)
        percentile_50 = np.percentile(final_values, 50)
        percentile_95 = np.percentile(final_values, 95)
        
        # Calculate probability of reaching target
        prob_target = (final_values >= target_amount).mean() * 100
        
        # Calculate other statistics
        mean_final_value = np.mean(final_values)
        std_final_value = np.std(final_values)
        
        # Store results
        monte_carlo_results[portfolio_key] = {
            'final_values': final_values,
            'all_paths': all_paths,
            'percentile_5': percentile_5,
            'percentile_50': percentile_50,
            'percentile_95': percentile_95,
            'mean_final_value': mean_final_value,
            'std_final_value': std_final_value,
            'prob_target': prob_target,
            'stats': stats
        }
        
        # Add to summary
        simulation_summary.append({
            'Risk Profile': stats['risk_profile'],
            'Horizon': stats['horizon'],
            'Mean Return': stats['mean_return'],
            'Volatility': stats['volatility'],
            '5th Percentile': percentile_5,
            '50th Percentile (Median)': percentile_50,
            '95th Percentile': percentile_95,
            'Mean Final Value': mean_final_value,
            'Probability of ₹25L': prob_target
        })
    
    #####################################################
    # STEP 5: DISPLAY RESULTS
    #####################################################
    print("\nSTEP 5: Displaying Monte Carlo simulation results...")
    
    # Create summary DataFrame
    summary_df = pd.DataFrame(simulation_summary)
    
    # Display results
    print("\nMONTE CARLO SIMULATION RESULTS (10 Years, ₹10,000 Monthly SIP):")
    print("=" * 80)
    
    # Format for display
    display_summary = summary_df.copy()
    display_summary['Mean Return'] = display_summary['Mean Return'].apply(lambda x: f"{x*100:.2f}%")
    display_summary['Volatility'] = display_summary['Volatility'].apply(lambda x: f"{x*100:.2f}%")
    display_summary['5th Percentile'] = display_summary['5th Percentile'].apply(lambda x: f"₹{x:,.0f}")
    display_summary['50th Percentile (Median)'] = display_summary['50th Percentile (Median)'].apply(lambda x: f"₹{x:,.0f}")
    display_summary['95th Percentile'] = display_summary['95th Percentile'].apply(lambda x: f"₹{x:,.0f}")
    display_summary['Mean Final Value'] = display_summary['Mean Final Value'].apply(lambda x: f"₹{x:,.0f}")
    display_summary['Probability of ₹25L'] = display_summary['Probability of ₹25L'].apply(lambda x: f"{x:.1f}%")
    
    print(tabulate(display_summary[['Risk Profile', 'Horizon', 'Mean Return', 'Volatility', 
                                   '5th Percentile', '50th Percentile (Median)', '95th Percentile', 
                                   'Probability of ₹25L']], 
                  headers='keys', tablefmt='grid', showindex=False))
    
    #####################################################
    # STEP 6: CREATE VISUALIZATIONS
    #####################################################
    print("\nSTEP 6: Creating Monte Carlo visualizations...")
    
    # Set up plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Create fan charts for each portfolio
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('Monte Carlo Simulation - Portfolio Value Projections (10 Years)', fontsize=16, fontweight='bold')
    
    portfolio_keys = list(monte_carlo_results.keys())
    
    for i, portfolio_key in enumerate(portfolio_keys):
        row = i // 3
        col = i % 3
        
        if row < 2 and col < 3:
            ax = axes[row, col]
            
            result = monte_carlo_results[portfolio_key]
            all_paths = result['all_paths']
            
            # Time axis (months)
            months = np.arange(all_paths.shape[1])
            years_axis = months / 12
            
            # Plot percentile bands
            percentiles = [5, 25, 50, 75, 95]
            path_percentiles = np.percentile(all_paths, percentiles, axis=0)
            
            # Fill between percentiles
            ax.fill_between(years_axis, path_percentiles[0], path_percentiles[4], 
                           alpha=0.2, label='5th-95th percentile', color='lightblue')
            ax.fill_between(years_axis, path_percentiles[1], path_percentiles[3], 
                           alpha=0.3, label='25th-75th percentile', color='lightgreen')
            
            # Plot median line
            ax.plot(years_axis, path_percentiles[2], linewidth=2, color='red', label='Median (50th percentile)')
            
            # Plot some sample paths
            sample_indices = np.random.choice(all_paths.shape[0], 50, replace=False)
            for idx in sample_indices:
                ax.plot(years_axis, all_paths[idx], alpha=0.1, color='gray', linewidth=0.5)
            
            # Add target line
            ax.axhline(y=target_amount, color='orange', linestyle='--', linewidth=2, 
                      label=f'Target (₹{target_amount/100000:.0f}L)')
            
            # Formatting
            portfolio_name, horizon = portfolio_key.split('_')
            ax.set_title(f'{portfolio_name} - {horizon} Term\n'
                        f'Prob. of ₹25L: {result["prob_target"]:.1f}%', fontweight='bold')
            ax.set_xlabel('Years')
            ax.set_ylabel('Portfolio Value (₹)')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
            # Format y-axis
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'₹{x/100000:.1f}L'))
    
    # Remove empty subplots
    for i in range(len(portfolio_keys), 6):
        row = i // 3
        col = i % 3
        if row < 2 and col < 3:
            fig.delaxes(axes[row, col])
    
    plt.tight_layout()
    plt.savefig('Monte_Carlo_Fan_Charts.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create probability comparison chart
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Prepare data for probability chart
    prob_data = summary_df.copy()
    prob_data['Portfolio'] = prob_data['Risk Profile'] + ' - ' + prob_data['Horizon']
    
    # Create bar chart
    bars = ax.bar(range(len(prob_data)), prob_data['Probability of ₹25L'], 
                  color=sns.color_palette("viridis", len(prob_data)))
    
    # Add value labels on bars
    for i, (bar, value) in enumerate(zip(bars, prob_data['Probability of ₹25L'])):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    ax.set_title('Probability of Reaching ₹25 Lakhs in 10 Years\n(₹10,000 Monthly SIP)', 
                fontsize=14, fontweight='bold')
    ax.set_xlabel('Portfolio Type')
    ax.set_ylabel('Probability (%)')
    ax.set_xticks(range(len(prob_data)))
    ax.set_xticklabels(prob_data['Portfolio'], rotation=45, ha='right')
    ax.set_ylim(0, 100)
    ax.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('Monte_Carlo_Probability_Chart.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create distribution comparison
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    fig.suptitle('Final Value Distributions After 10 Years', fontsize=16, fontweight='bold')
    
    for i, portfolio_key in enumerate(portfolio_keys):
        row = i // 3
        col = i % 3
        
        if row < 2 and col < 3:
            ax = axes[row, col]
            
            result = monte_carlo_results[portfolio_key]
            final_values = result['final_values']
            
            # Create histogram
            ax.hist(final_values, bins=50, alpha=0.7, density=True, color=sns.color_palette("husl")[i])
            
            # Add percentile lines
            ax.axvline(result['percentile_5'], color='red', linestyle='--', 
                      label=f'5th: ₹{result["percentile_5"]/100000:.1f}L')
            ax.axvline(result['percentile_50'], color='green', linestyle='-', 
                      label=f'50th: ₹{result["percentile_50"]/100000:.1f}L')
            ax.axvline(result['percentile_95'], color='blue', linestyle='--', 
                      label=f'95th: ₹{result["percentile_95"]/100000:.1f}L')
            ax.axvline(target_amount, color='orange', linestyle=':', 
                      label=f'Target: ₹{target_amount/100000:.0f}L')
            
            portfolio_name, horizon = portfolio_key.split('_')
            ax.set_title(f'{portfolio_name} - {horizon}', fontweight='bold')
            ax.set_xlabel('Final Portfolio Value (₹)')
            ax.set_ylabel('Probability Density')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
            # Format x-axis
            ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'₹{x/100000:.1f}L'))
    
    # Remove empty subplots
    for i in range(len(portfolio_keys), 6):
        row = i // 3
        col = i % 3
        if row < 2 and col < 3:
            fig.delaxes(axes[row, col])
    
    plt.tight_layout()
    plt.savefig('Monte_Carlo_Distributions.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Charts saved:")
    print("- 'Monte_Carlo_Fan_Charts.png': Projection paths with confidence bands")
    print("- 'Monte_Carlo_Probability_Chart.png': Probability of reaching ₹25L target")
    print("- 'Monte_Carlo_Distributions.png': Final value distributions")
    
    #####################################################
    # STEP 7: SAVE RESULTS
    #####################################################
    print("\nSTEP 7: Saving Monte Carlo simulation results...")
    
    # Save to Excel
    excel_file = 'Part4_Monte_Carlo_Simulation.xlsx'
    with pd.ExcelWriter(excel_file) as writer:
        # Save summary
        summary_df.to_excel(writer, sheet_name='Monte_Carlo_Summary', index=False)
        
        # Save detailed percentile analysis
        percentile_analysis = []
        for portfolio_key, result in monte_carlo_results.items():
            portfolio_name, horizon = portfolio_key.split('_')
            
            # Calculate additional percentiles
            percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
            percentile_values = np.percentile(result['final_values'], percentiles)
            
            for p, value in zip(percentiles, percentile_values):
                percentile_analysis.append({
                    'Risk Profile': portfolio_name,
                    'Horizon': horizon,
                    'Percentile': f'{p}th',
                    'Value': value
                })
        
        percentile_df = pd.DataFrame(percentile_analysis)
        percentile_df.to_excel(writer, sheet_name='Percentile_Analysis', index=False)
        
        # Save scenario analysis
        scenario_analysis = []
        scenarios = [
            ('Bear Market', 0.05),
            ('Below Average', 0.25),
            ('Average', 0.50),
            ('Above Average', 0.75),
            ('Bull Market', 0.95)
        ]
        
        for portfolio_key, result in monte_carlo_results.items():
            portfolio_name, horizon = portfolio_key.split('_')
            
            for scenario_name, percentile in scenarios:
                value = np.percentile(result['final_values'], percentile * 100)
                scenario_analysis.append({
                    'Risk Profile': portfolio_name,
                    'Horizon': horizon,
                    'Scenario': scenario_name,
                    'Portfolio Value': value,
                    'Total Invested': years * 12 * monthly_sip,
                    'Wealth Created': value - (years * 12 * monthly_sip),
                    'Return Multiple': value / (years * 12 * monthly_sip)
                })
        
        scenario_df = pd.DataFrame(scenario_analysis)
        scenario_df.to_excel(writer, sheet_name='Scenario_Analysis', index=False)
    
    print(f"Results saved to '{excel_file}'")
    
    #####################################################
    # STEP 8: KEY INSIGHTS
    #####################################################
    print("\nSTEP 8: Key insights from Monte Carlo simulation...")
    
    # Find best portfolios for different metrics
    best_median = summary_df.nlargest(1, '50th Percentile (Median)')
    best_prob = summary_df.nlargest(1, 'Probability of ₹25L')
    safest = summary_df.nsmallest(1, 'Volatility')
    
    print("\nKEY INSIGHTS FROM MONTE CARLO SIMULATION:")
    print("=" * 60)
    
    print(f"\n🎯 Target Analysis (₹25 Lakhs in 10 Years):")
    print(f"Total Investment Required: ₹{years * 12 * monthly_sip:,}")
    
    if len(best_prob) > 0:
        best_prob_row = best_prob.iloc[0]
        print(f"\nHighest Probability Portfolio:")
        print(f"  {best_prob_row['Risk Profile']} - {best_prob_row['Horizon']}")
        print(f"  Probability: {best_prob_row['Probability of ₹25L']:.1f}%")
        print(f"  Median Value: ₹{best_prob_row['50th Percentile (Median)']:,.0f}")
    
    if len(best_median) > 0:
        best_median_row = best_median.iloc[0]
        print(f"\nHighest Median Value Portfolio:")
        print(f"  {best_median_row['Risk Profile']} - {best_median_row['Horizon']}")
        print(f"  Median Value: ₹{best_median_row['50th Percentile (Median)']:,.0f}")
        print(f"  Probability of ₹25L: {best_median_row['Probability of ₹25L']:.1f}%")
    
    if len(safest) > 0:
        safest_row = safest.iloc[0]
        print(f"\nSafest Portfolio (Lowest Volatility):")
        print(f"  {safest_row['Risk Profile']} - {safest_row['Horizon']}")
        print(f"  Volatility: {safest_row['Volatility']*100:.2f}%")
        print(f"  Median Value: ₹{safest_row['50th Percentile (Median)']:,.0f}")
    
    # Risk-return analysis
    print(f"\n📊 Risk-Return Analysis:")
    avg_by_profile = summary_df.groupby('Risk Profile').agg({
        'Mean Return': 'mean',
        'Volatility': 'mean',
        '50th Percentile (Median)': 'mean',
        'Probability of ₹25L': 'mean'
    }).round(4)
    
    for profile in avg_by_profile.index:
        row = avg_by_profile.loc[profile]
        print(f"\n{profile}:")
        print(f"  Avg Return: {row['Mean Return']*100:.2f}%")
        print(f"  Avg Volatility: {row['Volatility']*100:.2f}%")
        print(f"  Avg Median Value: ₹{row['50th Percentile (Median)']:,.0f}")
        print(f"  Avg Prob. of ₹25L: {row['Probability of ₹25L']:.1f}%")
    
    # Confidence intervals
    print(f"\n📈 Confidence Intervals (90% confidence):")
    for _, row in summary_df.iterrows():
        print(f"\n{row['Risk Profile']} - {row['Horizon']}:")
        print(f"  90% of outcomes between: ₹{row['5th Percentile']:,.0f} - ₹{row['95th Percentile']:,.0f}")
        range_ratio = row['95th Percentile'] / row['5th Percentile']
        print(f"  Range ratio: {range_ratio:.1f}x")
    
    print("\nPart 4 Complete!")
    print("Next: Run Part 5 for Rebalancing Trigger Module")
    
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
