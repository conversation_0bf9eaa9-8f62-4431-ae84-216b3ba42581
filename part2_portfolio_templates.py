import pandas as pd
import numpy as np
from scipy.optimize import minimize
from tabulate import tabulate
import warnings
warnings.filterwarnings('ignore')

print("PART 2: PORTFOLIO TEMPLATES FOR ALL 6 RISK PROFILES")
print("=" * 70)

try:
    #####################################################
    # STEP 1: LOAD FUND METRICS FROM PART 1
    #####################################################
    print("\nSTEP 1: Loading fund metrics from Part 1...")
    
    # Load fund metrics
    metrics_df = pd.read_excel('Part1_Fund_Metrics.xlsx', sheet_name='Fund_Metrics')
    
    print(f"Loaded metrics for {len(metrics_df)} funds")
    
    # Load the original data for returns calculation
    file_path = "/Users/<USER>/Desktop/PythonProject1/venv/Portfolio Optimser.xlsx"
    df = pd.read_excel(file_path, sheet_name='Sheet2')
    df['date'] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    df = df.sort_index()
    df_filled = df.ffill().bfill()
    returns = df_filled.pct_change().dropna()
    returns_clean = returns.ffill().bfill()
    
    #####################################################
    # STEP 2: DEFINE RISK PROFILE CRITERIA
    #####################################################
    print("\nSTEP 2: Defining risk profile criteria...")
    
    # Define risk profiles with their constraints
    risk_profiles = {
        'Conservative': {
            'max_volatility': 0.12,      # 12% max volatility
            'max_drawdown': -0.12,       # 12% max drawdown
            'min_sharpe': 0.8,           # Minimum Sharpe ratio
            'target_return': 0.12,       # Target 12% return
            'equity_max': 0.40,          # Max 40% equity
            'debt_min': 0.50,            # Min 50% debt
        },
        'Balanced': {
            'max_volatility': 0.16,      # 16% max volatility
            'max_drawdown': -0.20,       # 20% max drawdown
            'min_sharpe': 1.0,           # Minimum Sharpe ratio
            'target_return': 0.15,       # Target 15% return
            'equity_max': 0.65,          # Max 65% equity
            'debt_min': 0.25,            # Min 25% debt
        },
        'Aggressive': {
            'max_volatility': 0.22,      # 22% max volatility
            'max_drawdown': -0.30,       # 30% max drawdown
            'min_sharpe': 1.2,           # Minimum Sharpe ratio
            'target_return': 0.18,       # Target 18% return
            'equity_max': 0.85,          # Max 85% equity
            'debt_min': 0.10,            # Min 10% debt
        }
    }
    
    # Time horizons affect fund selection criteria
    time_horizons = {
        'Short': {
            'focus_metric': '1Y CAGR',
            'stability_weight': 0.6,    # Higher weight on stability
            'return_weight': 0.4
        },
        'Long': {
            'focus_metric': '5Y CAGR',
            'stability_weight': 0.3,    # Lower weight on stability
            'return_weight': 0.7        # Higher weight on returns
        }
    }
    
    #####################################################
    # STEP 3: FUND SELECTION FOR EACH PROFILE
    #####################################################
    print("\nSTEP 3: Selecting funds for each risk profile...")
    
    def select_funds_for_profile(profile_name, profile_criteria, horizon_name, horizon_criteria):
        """Select appropriate funds for a given risk profile and time horizon"""
        
        print(f"\nSelecting funds for {profile_name} - {horizon_name} term...")
        
        # Filter funds based on risk criteria
        suitable_funds = metrics_df[
            (metrics_df['Ann. StDev'] <= profile_criteria['max_volatility']) &
            (metrics_df['Max Drawdown'] >= profile_criteria['max_drawdown']) &
            (metrics_df['Sharpe Ratio'] >= profile_criteria['min_sharpe'])
        ].copy()
        
        print(f"Funds meeting risk criteria: {len(suitable_funds)}")
        
        # If no funds meet strict criteria, relax constraints
        if len(suitable_funds) < 3:
            print("Relaxing constraints due to insufficient funds...")
            suitable_funds = metrics_df[
                (metrics_df['Ann. StDev'] <= profile_criteria['max_volatility'] * 1.2) &
                (metrics_df['Max Drawdown'] >= profile_criteria['max_drawdown'] * 1.2) &
                (metrics_df['Sharpe Ratio'] >= profile_criteria['min_sharpe'] * 0.8)
            ].copy()
            print(f"Funds after relaxing criteria: {len(suitable_funds)}")
        
        # If still insufficient, take top performers by category
        if len(suitable_funds) < 3:
            print("Using top performers by category...")
            
            # Get top funds from each category
            categories = ['Debt', 'Index', 'Flexi Cap', 'Mid Cap', 'Small Cap', 'Commodity']
            suitable_funds = pd.DataFrame()
            
            for category in categories:
                cat_funds = metrics_df[metrics_df['Category'] == category]
                if len(cat_funds) > 0:
                    # Select top 2 funds from each category by Sharpe ratio
                    top_cat_funds = cat_funds.nlargest(2, 'Sharpe Ratio')
                    suitable_funds = pd.concat([suitable_funds, top_cat_funds])
        
        # Calculate composite score based on time horizon
        focus_metric = horizon_criteria['focus_metric']
        stability_weight = horizon_criteria['stability_weight']
        return_weight = horizon_criteria['return_weight']
        
        # Normalize metrics for scoring
        if focus_metric in suitable_funds.columns:
            suitable_funds['Return_Score'] = (suitable_funds[focus_metric] - suitable_funds[focus_metric].min()) / (suitable_funds[focus_metric].max() - suitable_funds[focus_metric].min())
        else:
            suitable_funds['Return_Score'] = 0
            
        suitable_funds['Stability_Score'] = 1 - ((suitable_funds['Ann. StDev'] - suitable_funds['Ann. StDev'].min()) / (suitable_funds['Ann. StDev'].max() - suitable_funds['Ann. StDev'].min()))
        
        # Handle NaN values
        suitable_funds['Return_Score'] = suitable_funds['Return_Score'].fillna(0)
        suitable_funds['Stability_Score'] = suitable_funds['Stability_Score'].fillna(0.5)
        
        # Calculate composite score
        suitable_funds['Composite_Score'] = (
            return_weight * suitable_funds['Return_Score'] + 
            stability_weight * suitable_funds['Stability_Score']
        )
        
        # Sort by composite score
        suitable_funds = suitable_funds.sort_values('Composite_Score', ascending=False)
        
        return suitable_funds
    
    #####################################################
    # STEP 4: OPTIMIZE PORTFOLIOS
    #####################################################
    print("\nSTEP 4: Optimizing portfolios for each profile...")
    
    def optimize_portfolio(selected_funds, profile_criteria, profile_name, horizon_name):
        """Optimize portfolio weights for selected funds"""
        
        print(f"\nOptimizing {profile_name} - {horizon_name} portfolio...")
        
        if len(selected_funds) == 0:
            print("No suitable funds found!")
            return None
        
        # Get fund names
        fund_names = selected_funds['Fund Name'].tolist()
        
        # Calculate expected returns and covariance matrix
        fund_returns = returns_clean[fund_names]
        expected_returns = fund_returns.mean() * 252  # Annualized
        cov_matrix = fund_returns.cov() * 252  # Annualized
        
        # Define objective function (maximize Sharpe ratio)
        def negative_sharpe_ratio(weights, returns, cov_matrix, risk_free_rate=0.06):
            portfolio_return = np.sum(returns * weights)
            portfolio_volatility = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            
            if portfolio_volatility == 0:
                return 1000  # Large penalty
            
            sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_volatility
            return -sharpe_ratio
        
        # Define constraints
        def weight_sum_constraint(weights):
            return np.sum(weights) - 1
        
        def volatility_constraint(weights, returns, cov_matrix, max_vol):
            portfolio_volatility = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            return max_vol - portfolio_volatility
        
        def return_constraint(weights, returns, min_return):
            portfolio_return = np.sum(returns * weights)
            return portfolio_return - min_return
        
        # Asset class constraints
        def equity_constraint(weights, fund_names, selected_funds, max_equity):
            equity_categories = ['Index', 'Mid Cap', 'Small Cap', 'Flexi Cap']
            equity_weight = 0
            
            for i, fund_name in enumerate(fund_names):
                fund_category = selected_funds[selected_funds['Fund Name'] == fund_name]['Category'].iloc[0]
                if fund_category in equity_categories:
                    equity_weight += weights[i]
            
            return max_equity - equity_weight
        
        def debt_constraint(weights, fund_names, selected_funds, min_debt):
            debt_weight = 0
            
            for i, fund_name in enumerate(fund_names):
                fund_category = selected_funds[selected_funds['Fund Name'] == fund_name]['Category'].iloc[0]
                if fund_category == 'Debt':
                    debt_weight += weights[i]
            
            return debt_weight - min_debt
        
        # Initial guess (equal weights)
        n_funds = len(fund_names)
        initial_weights = np.array([1/n_funds] * n_funds)
        
        # Bounds (0% to 25% per fund for diversification)
        bounds = [(0.0, 0.25) for _ in range(n_funds)]
        
        # Constraints
        constraints = [
            {'type': 'eq', 'fun': weight_sum_constraint},
            {'type': 'ineq', 'fun': lambda w: volatility_constraint(w, expected_returns, cov_matrix, profile_criteria['max_volatility'])},
            {'type': 'ineq', 'fun': lambda w: return_constraint(w, expected_returns, profile_criteria['target_return'] * 0.8)},  # 80% of target
            {'type': 'ineq', 'fun': lambda w: equity_constraint(w, fund_names, selected_funds, profile_criteria['equity_max'])},
            {'type': 'ineq', 'fun': lambda w: debt_constraint(w, fund_names, selected_funds, profile_criteria['debt_min'])}
        ]
        
        # Run optimization
        try:
            result = minimize(
                negative_sharpe_ratio,
                initial_weights,
                args=(expected_returns, cov_matrix),
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000}
            )
            
            if result.success:
                optimal_weights = result.x
            else:
                print(f"Optimization failed: {result.message}")
                # Use equal weights as fallback
                optimal_weights = initial_weights
                
        except Exception as e:
            print(f"Optimization error: {str(e)}")
            optimal_weights = initial_weights
        
        # Filter out negligible weights and normalize
        significant_indices = optimal_weights >= 0.01
        if np.any(significant_indices):
            filtered_weights = optimal_weights[significant_indices]
            filtered_weights = filtered_weights / filtered_weights.sum()
            filtered_fund_names = [fund_names[i] for i in range(len(fund_names)) if significant_indices[i]]
        else:
            # If all weights are negligible, use top 3 funds equally
            top_3_indices = np.argsort(optimal_weights)[-3:]
            filtered_weights = np.array([1/3, 1/3, 1/3])
            filtered_fund_names = [fund_names[i] for i in top_3_indices]
        
        # Calculate portfolio metrics
        portfolio_return = np.sum(expected_returns[filtered_fund_names] * filtered_weights)
        portfolio_cov = cov_matrix.loc[filtered_fund_names, filtered_fund_names]
        portfolio_volatility = np.sqrt(np.dot(filtered_weights.T, np.dot(portfolio_cov, filtered_weights)))
        portfolio_sharpe = (portfolio_return - 0.06) / portfolio_volatility if portfolio_volatility > 0 else 0
        
        # Create result DataFrame
        portfolio_result = pd.DataFrame({
            'Fund Name': filtered_fund_names,
            'Category': [selected_funds[selected_funds['Fund Name'] == name]['Category'].iloc[0] for name in filtered_fund_names],
            'Weight %': filtered_weights * 100,
            'Expected CAGR': [expected_returns[name] for name in filtered_fund_names],
            'Sharpe': [selected_funds[selected_funds['Fund Name'] == name]['Sharpe Ratio'].iloc[0] for name in filtered_fund_names]
        })
        
        portfolio_result = portfolio_result.sort_values('Weight %', ascending=False)
        
        return {
            'portfolio': portfolio_result,
            'metrics': {
                'Expected Return': portfolio_return,
                'Volatility': portfolio_volatility,
                'Sharpe Ratio': portfolio_sharpe
            }
        }
    
    #####################################################
    # STEP 5: CREATE ALL 6 PORTFOLIOS
    #####################################################
    print("\nSTEP 5: Creating all 6 portfolio templates...")
    
    all_portfolios = {}
    portfolio_summary = []
    
    for profile_name, profile_criteria in risk_profiles.items():
        for horizon_name, horizon_criteria in time_horizons.items():
            
            # Select funds
            selected_funds = select_funds_for_profile(profile_name, profile_criteria, horizon_name, horizon_criteria)
            
            # Optimize portfolio
            portfolio_result = optimize_portfolio(selected_funds, profile_criteria, profile_name, horizon_name)
            
            if portfolio_result:
                portfolio_key = f"{profile_name}_{horizon_name}"
                all_portfolios[portfolio_key] = portfolio_result
                
                # Add to summary
                for _, row in portfolio_result['portfolio'].iterrows():
                    portfolio_summary.append({
                        'Risk Profile': profile_name,
                        'Horizon': horizon_name,
                        'Fund Name': row['Fund Name'],
                        'Category': row['Category'],
                        'Weight %': row['Weight %'],
                        'Expected CAGR': row['Expected CAGR'],
                        'Sharpe': row['Sharpe']
                    })
    
    #####################################################
    # STEP 6: DISPLAY RESULTS
    #####################################################
    print("\nSTEP 6: Displaying portfolio results...")
    
    for profile_name in risk_profiles.keys():
        for horizon_name in time_horizons.keys():
            portfolio_key = f"{profile_name}_{horizon_name}"
            
            if portfolio_key in all_portfolios:
                portfolio_data = all_portfolios[portfolio_key]
                
                print(f"\n{profile_name.upper()} - {horizon_name.upper()} TERM PORTFOLIO:")
                print("=" * 60)
                
                # Display portfolio composition
                display_portfolio = portfolio_data['portfolio'].copy()
                display_portfolio['Weight %'] = display_portfolio['Weight %'].apply(lambda x: f"{x:.2f}%")
                display_portfolio['Expected CAGR'] = display_portfolio['Expected CAGR'].apply(lambda x: f"{x*100:.2f}%")
                display_portfolio['Sharpe'] = display_portfolio['Sharpe'].apply(lambda x: f"{x:.3f}")
                
                print(tabulate(display_portfolio, headers='keys', tablefmt='grid', showindex=False))
                
                # Display portfolio metrics
                metrics = portfolio_data['metrics']
                print(f"\nPortfolio Metrics:")
                print(f"Expected Return: {metrics['Expected Return']*100:.2f}%")
                print(f"Volatility: {metrics['Volatility']*100:.2f}%")
                print(f"Sharpe Ratio: {metrics['Sharpe Ratio']:.3f}")
    
    #####################################################
    # STEP 7: SAVE RESULTS
    #####################################################
    print("\nSTEP 7: Saving results to Excel...")
    
    # Create summary DataFrame
    summary_df = pd.DataFrame(portfolio_summary)
    
    # Save to Excel
    excel_file = 'Part2_Portfolio_Templates.xlsx'
    with pd.ExcelWriter(excel_file) as writer:
        # Save summary
        summary_df.to_excel(writer, sheet_name='Portfolio_Allocations', index=False)
        
        # Save individual portfolios
        for portfolio_key, portfolio_data in all_portfolios.items():
            sheet_name = portfolio_key.replace('_', ' ')
            portfolio_data['portfolio'].to_excel(writer, sheet_name=sheet_name, index=False)
        
        # Save portfolio metrics summary
        metrics_summary = []
        for portfolio_key, portfolio_data in all_portfolios.items():
            profile, horizon = portfolio_key.split('_')
            metrics = portfolio_data['metrics']
            metrics_summary.append({
                'Risk Profile': profile,
                'Horizon': horizon,
                'Expected Return': metrics['Expected Return'],
                'Volatility': metrics['Volatility'],
                'Sharpe Ratio': metrics['Sharpe Ratio']
            })
        
        metrics_summary_df = pd.DataFrame(metrics_summary)
        metrics_summary_df.to_excel(writer, sheet_name='Portfolio_Metrics', index=False)
    
    print(f"Results saved to '{excel_file}'")
    
    print("\nPart 2 Complete!")
    print("Next: Run Part 3 for SIP Simulation")
    
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
