import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tabulate import tabulate
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

print("PART 5: REBALANCING TRIGGER MODULE")
print("=" * 60)

try:
    #####################################################
    # STEP 1: LOAD PORTFOLIO DATA AND NAV DATA
    #####################################################
    print("\nSTEP 1: Loading portfolio data and NAV data...")
    
    # Load portfolio allocations from Part 2
    portfolio_allocations = pd.read_excel('Part2_Portfolio_Templates.xlsx', sheet_name='Portfolio_Allocations')
    
    # Load the original NAV data
    file_path = "/Users/<USER>/Desktop/PythonProject1/venv/Portfolio Optimser.xlsx"
    df = pd.read_excel(file_path, sheet_name='Sheet2')
    df['date'] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    df = df.sort_index()
    df_filled = df.ffill().bfill()
    
    print(f"NAV data range: {df.index.min()} to {df.index.max()}")
    print(f"Portfolio templates loaded: {len(portfolio_allocations)}")
    
    # Get unique portfolio combinations
    portfolios = portfolio_allocations[['Risk Profile', 'Horizon']].drop_duplicates()
    
    #####################################################
    # STEP 2: SIMULATE PORTFOLIO DRIFT OVER TIME
    #####################################################
    print("\nSTEP 2: Simulating portfolio drift over time...")
    
    def simulate_portfolio_drift(portfolio_name, horizon, rebalance_threshold=0.05):
        """
        Simulate how portfolio weights drift over time and identify rebalancing triggers
        
        Parameters:
        - portfolio_name: Risk profile name
        - horizon: Investment horizon
        - rebalance_threshold: Threshold for triggering rebalance (default 5%)
        """
        
        # Get fund allocations for this portfolio
        portfolio_funds = portfolio_allocations[
            (portfolio_allocations['Risk Profile'] == portfolio_name) & 
            (portfolio_allocations['Horizon'] == horizon)
        ].copy()
        
        if len(portfolio_funds) == 0:
            return None
        
        # Initial weights
        initial_weights = {}
        for _, fund_row in portfolio_funds.iterrows():
            initial_weights[fund_row['Fund Name']] = fund_row['Weight %'] / 100
        
        # Simulate portfolio over time with 6-month rebalancing checks
        start_date = df.index[0]
        end_date = df.index[-1]
        
        # Create 6-month intervals
        rebalance_dates = []
        current_date = start_date
        while current_date <= end_date:
            rebalance_dates.append(current_date)
            current_date += timedelta(days=180)  # Approximately 6 months
        
        # Filter dates that exist in our data
        rebalance_dates = [date for date in rebalance_dates if date in df.index]
        
        rebalancing_history = []
        portfolio_value_history = []
        
        # Initial portfolio value (assume ₹100,000 investment)
        initial_investment = 100000
        current_units = {}
        
        # Calculate initial units for each fund
        for fund_name, weight in initial_weights.items():
            if fund_name in df.columns:
                fund_investment = initial_investment * weight
                initial_nav = df[fund_name].iloc[0]
                current_units[fund_name] = fund_investment / initial_nav
        
        # Track portfolio over time
        for i, check_date in enumerate(rebalance_dates):
            if check_date not in df.index:
                continue
                
            # Calculate current portfolio value and weights
            total_value = 0
            current_values = {}
            
            for fund_name in current_units.keys():
                if fund_name in df.columns:
                    current_nav = df[fund_name].loc[check_date]
                    fund_value = current_units[fund_name] * current_nav
                    current_values[fund_name] = fund_value
                    total_value += fund_value
            
            # Calculate current weights
            current_weights = {}
            for fund_name, value in current_values.items():
                current_weights[fund_name] = value / total_value if total_value > 0 else 0
            
            # Check for drift
            rebalance_needed = False
            drift_details = []
            
            for fund_name in initial_weights.keys():
                if fund_name in current_weights:
                    initial_weight = initial_weights[fund_name]
                    current_weight = current_weights[fund_name]
                    drift = abs(current_weight - initial_weight)
                    
                    if drift > rebalance_threshold:
                        rebalance_needed = True
                    
                    drift_details.append({
                        'Fund Name': fund_name,
                        'Ideal Weight': initial_weight,
                        'Current Weight': current_weight,
                        'Drift': current_weight - initial_weight,
                        'Abs Drift': drift,
                        'Rebalance Needed': drift > rebalance_threshold
                    })
            
            # Record rebalancing event
            rebalancing_event = {
                'Date': check_date,
                'Portfolio Value': total_value,
                'Rebalance Needed': rebalance_needed,
                'Max Drift': max([d['Abs Drift'] for d in drift_details]) if drift_details else 0,
                'Funds Needing Rebalance': len([d for d in drift_details if d['Rebalance Needed']]),
                'Drift Details': drift_details
            }
            
            rebalancing_history.append(rebalancing_event)
            
            # If rebalancing is needed, rebalance the portfolio
            if rebalance_needed:
                print(f"  Rebalancing triggered on {check_date.strftime('%Y-%m-%d')} for {portfolio_name}-{horizon}")
                
                # Rebalance: adjust units to match target weights
                for fund_name in initial_weights.keys():
                    if fund_name in df.columns:
                        target_value = total_value * initial_weights[fund_name]
                        current_nav = df[fund_name].loc[check_date]
                        target_units = target_value / current_nav
                        current_units[fund_name] = target_units
            
            # Record portfolio value
            portfolio_value_history.append({
                'Date': check_date,
                'Portfolio Value': total_value,
                'Rebalanced': rebalance_needed
            })
        
        return {
            'rebalancing_history': rebalancing_history,
            'portfolio_value_history': portfolio_value_history,
            'initial_weights': initial_weights
        }
    
    #####################################################
    # STEP 3: RUN REBALANCING SIMULATION FOR ALL PORTFOLIOS
    #####################################################
    print("\nSTEP 3: Running rebalancing simulation for all portfolios...")
    
    rebalancing_results = {}
    rebalancing_summary = []
    
    for _, row in portfolios.iterrows():
        portfolio_key = f"{row['Risk Profile']}_{row['Horizon']}"
        print(f"\nSimulating rebalancing for {portfolio_key}...")
        
        result = simulate_portfolio_drift(row['Risk Profile'], row['Horizon'])
        
        if result:
            rebalancing_results[portfolio_key] = result
            
            # Calculate summary statistics
            rebalance_events = [event for event in result['rebalancing_history'] if event['Rebalance Needed']]
            total_checks = len(result['rebalancing_history'])
            
            if len(result['portfolio_value_history']) > 0:
                initial_value = result['portfolio_value_history'][0]['Portfolio Value']
                final_value = result['portfolio_value_history'][-1]['Portfolio Value']
                total_return = (final_value / initial_value - 1) * 100
            else:
                total_return = 0
            
            avg_drift = np.mean([event['Max Drift'] for event in result['rebalancing_history']])
            max_drift = max([event['Max Drift'] for event in result['rebalancing_history']])
            
            rebalancing_summary.append({
                'Risk Profile': row['Risk Profile'],
                'Horizon': row['Horizon'],
                'Total Checks': total_checks,
                'Rebalance Events': len(rebalance_events),
                'Rebalance Frequency': len(rebalance_events) / total_checks * 100,
                'Average Drift': avg_drift,
                'Maximum Drift': max_drift,
                'Total Return': total_return
            })
    
    #####################################################
    # STEP 4: GENERATE CURRENT REBALANCING RECOMMENDATIONS
    #####################################################
    print("\nSTEP 4: Generating current rebalancing recommendations...")
    
    def generate_rebalancing_recommendations(portfolio_name, horizon, current_date=None):
        """Generate rebalancing recommendations for current date"""
        
        if current_date is None:
            current_date = df.index[-1]  # Use latest available date
        
        # Get fund allocations for this portfolio
        portfolio_funds = portfolio_allocations[
            (portfolio_allocations['Risk Profile'] == portfolio_name) & 
            (portfolio_allocations['Horizon'] == horizon)
        ].copy()
        
        if len(portfolio_funds) == 0:
            return None
        
        # Simulate some drift (for demonstration purposes)
        # In real implementation, this would come from actual portfolio tracking
        np.random.seed(42)  # For reproducible results
        
        recommendations = []
        total_portfolio_value = 1000000  # Assume ₹10 lakh portfolio
        
        for _, fund_row in portfolio_funds.iterrows():
            fund_name = fund_row['Fund Name']
            ideal_weight = fund_row['Weight %'] / 100
            
            # Simulate current weight with some random drift
            drift_factor = np.random.normal(0, 0.03)  # Random drift up to ±3%
            current_weight = max(0, ideal_weight + drift_factor)
            
            # Normalize weights (ensure they sum to 1)
            current_weight = current_weight / portfolio_funds['Weight %'].sum() * 100
            
            ideal_value = total_portfolio_value * ideal_weight
            current_value = total_portfolio_value * (current_weight / 100)
            
            difference = current_value - ideal_value
            action = 'Hold'
            
            if abs(difference) > total_portfolio_value * 0.05 * ideal_weight:  # 5% threshold
                if difference > 0:
                    action = 'Sell'
                else:
                    action = 'Buy'
            
            recommendations.append({
                'Fund Name': fund_name,
                'Category': fund_row['Category'],
                'Ideal %': ideal_weight * 100,
                'Current %': current_weight,
                'Ideal Value': ideal_value,
                'Current Value': current_value,
                'Difference': difference,
                'Action': action,
                'Amount': abs(difference)
            })
        
        return recommendations
    
    # Generate recommendations for all portfolios
    current_recommendations = {}
    
    for _, row in portfolios.iterrows():
        portfolio_key = f"{row['Risk Profile']}_{row['Horizon']}"
        recommendations = generate_rebalancing_recommendations(row['Risk Profile'], row['Horizon'])
        
        if recommendations:
            current_recommendations[portfolio_key] = recommendations
    
    #####################################################
    # STEP 5: DISPLAY RESULTS
    #####################################################
    print("\nSTEP 5: Displaying rebalancing analysis results...")
    
    # Display rebalancing summary
    summary_df = pd.DataFrame(rebalancing_summary)
    
    print("\nREBALANCING FREQUENCY ANALYSIS:")
    print("=" * 60)
    
    # Format for display
    display_summary = summary_df.copy()
    display_summary['Rebalance Frequency'] = display_summary['Rebalance Frequency'].apply(lambda x: f"{x:.1f}%")
    display_summary['Average Drift'] = display_summary['Average Drift'].apply(lambda x: f"{x*100:.2f}%")
    display_summary['Maximum Drift'] = display_summary['Maximum Drift'].apply(lambda x: f"{x*100:.2f}%")
    display_summary['Total Return'] = display_summary['Total Return'].apply(lambda x: f"{x:.2f}%")
    
    print(tabulate(display_summary[['Risk Profile', 'Horizon', 'Total Checks', 'Rebalance Events', 
                                   'Rebalance Frequency', 'Average Drift', 'Maximum Drift', 'Total Return']], 
                  headers='keys', tablefmt='grid', showindex=False))
    
    # Display current recommendations for each portfolio
    print("\n\nCURRENT REBALANCING RECOMMENDATIONS:")
    print("=" * 70)
    
    for portfolio_key, recommendations in current_recommendations.items():
        portfolio_name, horizon = portfolio_key.split('_')
        
        print(f"\n{portfolio_name.upper()} - {horizon.upper()} TERM PORTFOLIO:")
        print("-" * 50)
        
        recommendations_df = pd.DataFrame(recommendations)
        
        # Format for display
        display_recommendations = recommendations_df.copy()
        display_recommendations['Ideal %'] = display_recommendations['Ideal %'].apply(lambda x: f"{x:.2f}%")
        display_recommendations['Current %'] = display_recommendations['Current %'].apply(lambda x: f"{x:.2f}%")
        display_recommendations['Ideal Value'] = display_recommendations['Ideal Value'].apply(lambda x: f"₹{x:,.0f}")
        display_recommendations['Current Value'] = display_recommendations['Current Value'].apply(lambda x: f"₹{x:,.0f}")
        display_recommendations['Difference'] = display_recommendations['Difference'].apply(lambda x: f"₹{x:,.0f}")
        display_recommendations['Amount'] = display_recommendations['Amount'].apply(lambda x: f"₹{x:,.0f}")
        
        # Only show funds that need action
        action_needed = display_recommendations[display_recommendations['Action'] != 'Hold']
        
        if len(action_needed) > 0:
            print("Funds requiring rebalancing:")
            print(tabulate(action_needed[['Fund Name', 'Ideal %', 'Current %', 'Action', 'Amount']], 
                          headers='keys', tablefmt='grid', showindex=False))
        else:
            print("No rebalancing required - all funds within 5% threshold.")
        
        # Show summary
        total_buy = recommendations_df[recommendations_df['Action'] == 'Buy']['Amount'].sum()
        total_sell = recommendations_df[recommendations_df['Action'] == 'Sell']['Amount'].sum()
        
        print(f"\nRebalancing Summary:")
        print(f"Total to Buy: ₹{total_buy:,.0f}")
        print(f"Total to Sell: ₹{total_sell:,.0f}")
        print(f"Net Cash Flow: ₹{total_sell - total_buy:,.0f}")
    
    #####################################################
    # STEP 6: CREATE VISUALIZATIONS
    #####################################################
    print("\nSTEP 6: Creating rebalancing visualizations...")
    
    # Set up plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Create drift analysis chart
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Portfolio Drift Analysis Over Time', fontsize=16, fontweight='bold')
    
    portfolio_keys = list(rebalancing_results.keys())
    
    for i, portfolio_key in enumerate(portfolio_keys):
        row = i // 3
        col = i % 3
        
        if row < 2 and col < 3:
            ax = axes[row, col]
            
            result = rebalancing_results[portfolio_key]
            history = result['rebalancing_history']
            
            # Extract data for plotting
            dates = [event['Date'] for event in history]
            max_drifts = [event['Max Drift'] * 100 for event in history]
            rebalance_points = [event['Date'] for event in history if event['Rebalance Needed']]
            
            # Plot drift over time
            ax.plot(dates, max_drifts, linewidth=2, label='Maximum Drift')
            ax.axhline(y=5, color='red', linestyle='--', label='Rebalance Threshold (5%)')
            
            # Mark rebalancing points
            for rebalance_date in rebalance_points:
                ax.axvline(x=rebalance_date, color='orange', alpha=0.7, linestyle=':', linewidth=1)
            
            portfolio_name, horizon = portfolio_key.split('_')
            ax.set_title(f'{portfolio_name} - {horizon} Term', fontweight='bold')
            ax.set_xlabel('Date')
            ax.set_ylabel('Maximum Drift (%)')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # Rotate x-axis labels
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    # Remove empty subplots
    for i in range(len(portfolio_keys), 6):
        row = i // 3
        col = i % 3
        if row < 2 and col < 3:
            fig.delaxes(axes[row, col])
    
    plt.tight_layout()
    plt.savefig('Portfolio_Drift_Analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create rebalancing frequency comparison
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Prepare data for comparison
    comparison_data = summary_df.copy()
    comparison_data['Portfolio'] = comparison_data['Risk Profile'] + ' - ' + comparison_data['Horizon']
    
    # Create bar chart
    bars = ax.bar(range(len(comparison_data)), comparison_data['Rebalance Frequency'], 
                  color=sns.color_palette("viridis", len(comparison_data)))
    
    # Add value labels on bars
    for i, (bar, value) in enumerate(zip(bars, comparison_data['Rebalance Frequency'])):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    ax.set_title('Rebalancing Frequency by Portfolio Type\n(Percentage of 6-month periods requiring rebalancing)', 
                fontsize=14, fontweight='bold')
    ax.set_xlabel('Portfolio Type')
    ax.set_ylabel('Rebalancing Frequency (%)')
    ax.set_xticks(range(len(comparison_data)))
    ax.set_xticklabels(comparison_data['Portfolio'], rotation=45, ha='right')
    ax.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('Rebalancing_Frequency_Comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Charts saved:")
    print("- 'Portfolio_Drift_Analysis.png': Drift patterns over time")
    print("- 'Rebalancing_Frequency_Comparison.png': Frequency comparison across portfolios")
    
    #####################################################
    # STEP 7: SAVE RESULTS
    #####################################################
    print("\nSTEP 7: Saving rebalancing analysis results...")
    
    # Save to Excel
    excel_file = 'Part5_Rebalancing_Analysis.xlsx'
    with pd.ExcelWriter(excel_file) as writer:
        # Save rebalancing summary
        summary_df.to_excel(writer, sheet_name='Rebalancing_Summary', index=False)
        
        # Save current recommendations for each portfolio
        for portfolio_key, recommendations in current_recommendations.items():
            sheet_name = portfolio_key.replace('_', ' ')[:31]  # Excel sheet name limit
            recommendations_df = pd.DataFrame(recommendations)
            recommendations_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # Save detailed rebalancing history
        detailed_history = []
        for portfolio_key, result in rebalancing_results.items():
            portfolio_name, horizon = portfolio_key.split('_')
            
            for event in result['rebalancing_history']:
                for detail in event['Drift Details']:
                    detailed_history.append({
                        'Portfolio': f"{portfolio_name} - {horizon}",
                        'Date': event['Date'],
                        'Fund Name': detail['Fund Name'],
                        'Ideal Weight': detail['Ideal Weight'],
                        'Current Weight': detail['Current Weight'],
                        'Drift': detail['Drift'],
                        'Abs Drift': detail['Abs Drift'],
                        'Rebalance Needed': detail['Rebalance Needed'],
                        'Portfolio Value': event['Portfolio Value']
                    })
        
        if detailed_history:
            detailed_df = pd.DataFrame(detailed_history)
            detailed_df.to_excel(writer, sheet_name='Detailed_History', index=False)
        
        # Save rebalancing triggers summary
        triggers_summary = []
        for portfolio_key, result in rebalancing_results.items():
            portfolio_name, horizon = portfolio_key.split('_')
            
            rebalance_events = [event for event in result['rebalancing_history'] if event['Rebalance Needed']]
            
            for event in rebalance_events:
                triggers_summary.append({
                    'Portfolio': f"{portfolio_name} - {horizon}",
                    'Date': event['Date'],
                    'Portfolio Value': event['Portfolio Value'],
                    'Max Drift': event['Max Drift'],
                    'Funds Affected': event['Funds Needing Rebalance']
                })
        
        if triggers_summary:
            triggers_df = pd.DataFrame(triggers_summary)
            triggers_df.to_excel(writer, sheet_name='Rebalancing_Triggers', index=False)
    
    print(f"Results saved to '{excel_file}'")
    
    #####################################################
    # STEP 8: KEY INSIGHTS
    #####################################################
    print("\nSTEP 8: Key insights from rebalancing analysis...")
    
    print("\nKEY INSIGHTS FROM REBALANCING ANALYSIS:")
    print("=" * 60)
    
    # Find portfolios with different rebalancing characteristics
    most_frequent = summary_df.nlargest(1, 'Rebalance Frequency')
    least_frequent = summary_df.nsmallest(1, 'Rebalance Frequency')
    highest_drift = summary_df.nlargest(1, 'Maximum Drift')
    
    if len(most_frequent) > 0:
        most_freq_row = most_frequent.iloc[0]
        print(f"\nMost Frequent Rebalancing:")
        print(f"  {most_freq_row['Risk Profile']} - {most_freq_row['Horizon']}")
        print(f"  Frequency: {most_freq_row['Rebalance Frequency']:.1f}% of periods")
        print(f"  Average Drift: {most_freq_row['Average Drift']*100:.2f}%")
    
    if len(least_frequent) > 0:
        least_freq_row = least_frequent.iloc[0]
        print(f"\nLeast Frequent Rebalancing:")
        print(f"  {least_freq_row['Risk Profile']} - {least_freq_row['Horizon']}")
        print(f"  Frequency: {least_freq_row['Rebalance Frequency']:.1f}% of periods")
        print(f"  Average Drift: {least_freq_row['Average Drift']*100:.2f}%")
    
    if len(highest_drift) > 0:
        highest_drift_row = highest_drift.iloc[0]
        print(f"\nHighest Maximum Drift:")
        print(f"  {highest_drift_row['Risk Profile']} - {highest_drift_row['Horizon']}")
        print(f"  Maximum Drift: {highest_drift_row['Maximum Drift']*100:.2f}%")
    
    # General insights
    avg_frequency = summary_df['Rebalance Frequency'].mean()
    avg_drift = summary_df['Average Drift'].mean()
    
    print(f"\nGeneral Patterns:")
    print(f"  Average Rebalancing Frequency: {avg_frequency:.1f}% of 6-month periods")
    print(f"  Average Portfolio Drift: {avg_drift*100:.2f}%")
    print(f"  Rebalancing Threshold Used: 5%")
    
    # Risk profile analysis
    print(f"\nRebalancing by Risk Profile:")
    risk_analysis = summary_df.groupby('Risk Profile').agg({
        'Rebalance Frequency': 'mean',
        'Average Drift': 'mean',
        'Maximum Drift': 'mean'
    }).round(4)
    
    for profile in risk_analysis.index:
        row = risk_analysis.loc[profile]
        print(f"\n{profile}:")
        print(f"  Avg Rebalancing Frequency: {row['Rebalance Frequency']:.1f}%")
        print(f"  Avg Drift: {row['Average Drift']*100:.2f}%")
        print(f"  Avg Max Drift: {row['Maximum Drift']*100:.2f}%")
    
    # Recommendations
    print(f"\n💡 Rebalancing Recommendations:")
    print(f"  • Monitor portfolios every 6 months")
    print(f"  • Rebalance when any fund drifts >5% from target")
    print(f"  • Aggressive portfolios may need more frequent monitoring")
    print(f"  • Conservative portfolios typically require less rebalancing")
    print(f"  • Consider transaction costs when rebalancing small amounts")
    
    print("\nPart 5 Complete!")
    print("Next: Run Part 6 for Final Export and Consolidation")
    
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
