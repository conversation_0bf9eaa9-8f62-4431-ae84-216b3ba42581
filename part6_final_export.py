import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from openpyxl import load_workbook
from openpyxl.styles import Font, Ali<PERSON>ment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
import warnings
warnings.filterwarnings('ignore')

print("PART 6: FINAL EXPORT AND CONSOLIDATION")
print("=" * 60)

try:
    #####################################################
    # STEP 1: LOAD ALL RESULTS FROM PREVIOUS PARTS
    #####################################################
    print("\nSTEP 1: Loading all results from previous parts...")
    
    # Load all Excel files from previous parts
    try:
        fund_metrics = pd.read_excel('Part1_Fund_Metrics.xlsx', sheet_name='Fund_Metrics')
        print("✓ Part 1: Fund Metrics loaded")
    except:
        print("✗ Part 1: Fund Metrics not found")
        fund_metrics = pd.DataFrame()
    
    try:
        portfolio_allocations = pd.read_excel('Part2_Portfolio_Templates.xlsx', sheet_name='Portfolio_Allocations')
        portfolio_metrics = pd.read_excel('Part2_Portfolio_Templates.xlsx', sheet_name='Portfolio_Metrics')
        print("✓ Part 2: Portfolio Templates loaded")
    except:
        print("✗ Part 2: Portfolio Templates not found")
        portfolio_allocations = pd.DataFrame()
        portfolio_metrics = pd.DataFrame()
    
    try:
        sip_summary = pd.read_excel('Part3_SIP_Simulation.xlsx', sheet_name='SIP_Summary')
        print("✓ Part 3: SIP Simulation loaded")
    except:
        print("✗ Part 3: SIP Simulation not found")
        sip_summary = pd.DataFrame()
    
    try:
        monte_carlo_summary = pd.read_excel('Part4_Monte_Carlo_Simulation.xlsx', sheet_name='Monte_Carlo_Summary')
        percentile_analysis = pd.read_excel('Part4_Monte_Carlo_Simulation.xlsx', sheet_name='Percentile_Analysis')
        scenario_analysis = pd.read_excel('Part4_Monte_Carlo_Simulation.xlsx', sheet_name='Scenario_Analysis')
        print("✓ Part 4: Monte Carlo Simulation loaded")
    except:
        print("✗ Part 4: Monte Carlo Simulation not found")
        monte_carlo_summary = pd.DataFrame()
        percentile_analysis = pd.DataFrame()
        scenario_analysis = pd.DataFrame()
    
    try:
        rebalancing_summary = pd.read_excel('Part5_Rebalancing_Analysis.xlsx', sheet_name='Rebalancing_Summary')
        print("✓ Part 5: Rebalancing Analysis loaded")
    except:
        print("✗ Part 5: Rebalancing Analysis not found")
        rebalancing_summary = pd.DataFrame()
    
    #####################################################
    # STEP 2: CREATE EXECUTIVE SUMMARY
    #####################################################
    print("\nSTEP 2: Creating executive summary...")
    
    # Create executive summary with key insights
    executive_summary = []
    
    # Fund analysis summary
    if not fund_metrics.empty:
        top_sharpe_fund = fund_metrics.nlargest(1, 'Sharpe Ratio').iloc[0]
        top_return_fund = fund_metrics.nlargest(1, '1Y CAGR').iloc[0]
        
        executive_summary.extend([
            {'Category': 'Fund Analysis', 'Metric': 'Total Funds Analyzed', 'Value': len(fund_metrics)},
            {'Category': 'Fund Analysis', 'Metric': 'Best Sharpe Ratio Fund', 'Value': f"{top_sharpe_fund['Fund Name']} ({top_sharpe_fund['Sharpe Ratio']:.3f})"},
            {'Category': 'Fund Analysis', 'Metric': 'Highest Return Fund', 'Value': f"{top_return_fund['Fund Name']} ({top_return_fund['1Y CAGR']*100:.2f}%)"},
        ])
    
    # Portfolio analysis summary
    if not portfolio_metrics.empty:
        best_portfolio = portfolio_metrics.nlargest(1, 'Sharpe Ratio').iloc[0]
        
        executive_summary.extend([
            {'Category': 'Portfolio Analysis', 'Metric': 'Portfolio Templates Created', 'Value': len(portfolio_metrics)},
            {'Category': 'Portfolio Analysis', 'Metric': 'Best Portfolio', 'Value': f"{best_portfolio['Risk Profile']} - {best_portfolio['Horizon']}"},
            {'Category': 'Portfolio Analysis', 'Metric': 'Best Portfolio Sharpe', 'Value': f"{best_portfolio['Sharpe Ratio']:.3f}"},
        ])
    
    # SIP analysis summary
    if not sip_summary.empty:
        best_sip = sip_summary.nlargest(1, 'Annualized Return %').iloc[0]
        
        executive_summary.extend([
            {'Category': 'SIP Analysis', 'Metric': 'SIP Scenarios Analyzed', 'Value': len(sip_summary)},
            {'Category': 'SIP Analysis', 'Metric': 'Best SIP Performance', 'Value': f"{best_sip['Risk Profile']} - {best_sip['Horizon']} ({best_sip['Investment Period']})"},
            {'Category': 'SIP Analysis', 'Metric': 'Best SIP Return', 'Value': f"{best_sip['Annualized Return %']:.2f}%"},
        ])
    
    # Monte Carlo summary
    if not monte_carlo_summary.empty:
        best_prob = monte_carlo_summary.nlargest(1, 'Probability of ₹25L').iloc[0]
        
        executive_summary.extend([
            {'Category': 'Monte Carlo Analysis', 'Metric': 'Simulations Run', 'Value': '10,000 per portfolio'},
            {'Category': 'Monte Carlo Analysis', 'Metric': 'Best Probability for ₹25L Target', 'Value': f"{best_prob['Risk Profile']} - {best_prob['Horizon']} ({best_prob['Probability of ₹25L']:.1f}%)"},
            {'Category': 'Monte Carlo Analysis', 'Metric': 'Investment Horizon', 'Value': '10 Years'},
        ])
    
    # Rebalancing summary
    if not rebalancing_summary.empty:
        avg_frequency = rebalancing_summary['Rebalance Frequency'].mean()
        
        executive_summary.extend([
            {'Category': 'Rebalancing Analysis', 'Metric': 'Average Rebalancing Frequency', 'Value': f"{avg_frequency:.1f}% of 6-month periods"},
            {'Category': 'Rebalancing Analysis', 'Metric': 'Rebalancing Threshold', 'Value': '5% drift from target'},
            {'Category': 'Rebalancing Analysis', 'Metric': 'Monitoring Frequency', 'Value': 'Every 6 months'},
        ])
    
    executive_summary_df = pd.DataFrame(executive_summary)
    
    #####################################################
    # STEP 3: CREATE COMPREHENSIVE RECOMMENDATIONS
    #####################################################
    print("\nSTEP 3: Creating comprehensive recommendations...")
    
    recommendations = []
    
    # Investment recommendations based on risk profile
    if not monte_carlo_summary.empty and not sip_summary.empty:
        
        # Conservative investors
        conservative_mc = monte_carlo_summary[monte_carlo_summary['Risk Profile'] == 'Conservative']
        if not conservative_mc.empty:
            conservative_rec = conservative_mc.iloc[0]
            recommendations.append({
                'Risk Profile': 'Conservative',
                'Recommendation': 'Debt-focused portfolio with stable returns',
                'Expected Return': f"{conservative_rec['Mean Return']*100:.2f}%",
                'Risk Level': 'Low',
                'Suitable For': 'Risk-averse investors, short-term goals, capital preservation',
                'Key Benefit': 'Predictable returns with minimal volatility'
            })
        
        # Balanced investors
        balanced_mc = monte_carlo_summary[monte_carlo_summary['Risk Profile'] == 'Balanced']
        if not balanced_mc.empty:
            balanced_rec = balanced_mc.iloc[0]
            recommendations.append({
                'Risk Profile': 'Balanced',
                'Recommendation': 'Mixed allocation with good risk-return balance',
                'Expected Return': f"{balanced_rec['Mean Return']*100:.2f}%",
                'Risk Level': 'Moderate',
                'Suitable For': 'Moderate risk tolerance, medium-term goals, balanced approach',
                'Key Benefit': f"{balanced_rec['Probability of ₹25L']:.1f}% chance of reaching ₹25L target"
            })
        
        # Aggressive investors
        aggressive_mc = monte_carlo_summary[monte_carlo_summary['Risk Profile'] == 'Aggressive']
        if not aggressive_mc.empty:
            aggressive_rec = aggressive_mc.iloc[0]
            recommendations.append({
                'Risk Profile': 'Aggressive',
                'Recommendation': 'Equity-heavy portfolio for maximum growth',
                'Expected Return': f"{aggressive_rec['Mean Return']*100:.2f}%",
                'Risk Level': 'High',
                'Suitable For': 'High risk tolerance, long-term goals, wealth creation',
                'Key Benefit': f"{aggressive_rec['Probability of ₹25L']:.1f}% chance of reaching ₹25L target"
            })
    
    recommendations_df = pd.DataFrame(recommendations)
    
    #####################################################
    # STEP 4: CREATE FINAL CONSOLIDATED WORKBOOK
    #####################################################
    print("\nSTEP 4: Creating final consolidated workbook...")
    
    # Create the main Excel file
    excel_file = 'Financial_Analytics_Complete_Report.xlsx'
    
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        
        # Sheet 1: Executive Summary
        if not executive_summary_df.empty:
            executive_summary_df.to_excel(writer, sheet_name='Executive_Summary', index=False)
        
        # Sheet 2: Investment Recommendations
        if not recommendations_df.empty:
            recommendations_df.to_excel(writer, sheet_name='Investment_Recommendations', index=False)
        
        # Sheet 3: Fund Metrics (from Part 1)
        if not fund_metrics.empty:
            fund_metrics.to_excel(writer, sheet_name='Fund_Metrics', index=False)
        
        # Sheet 4: Portfolio Allocations (from Part 2)
        if not portfolio_allocations.empty:
            portfolio_allocations.to_excel(writer, sheet_name='Portfolio_Allocations', index=False)
        
        # Sheet 5: Portfolio Metrics (from Part 2)
        if not portfolio_metrics.empty:
            portfolio_metrics.to_excel(writer, sheet_name='Portfolio_Metrics', index=False)
        
        # Sheet 6: SIP Simulation (from Part 3)
        if not sip_summary.empty:
            sip_summary.to_excel(writer, sheet_name='SIP_Simulation', index=False)
        
        # Sheet 7: Monte Carlo Output (from Part 4)
        if not monte_carlo_summary.empty:
            monte_carlo_summary.to_excel(writer, sheet_name='MonteCarlo_Output', index=False)
        
        # Sheet 8: Scenario Analysis (from Part 4)
        if not scenario_analysis.empty:
            scenario_analysis.to_excel(writer, sheet_name='Scenario_Analysis', index=False)
        
        # Sheet 9: Rebalancing Suggestions (from Part 5)
        if not rebalancing_summary.empty:
            rebalancing_summary.to_excel(writer, sheet_name='Rebalancing_Suggestions', index=False)
        
        # Sheet 10: Percentile Analysis (from Part 4)
        if not percentile_analysis.empty:
            percentile_analysis.to_excel(writer, sheet_name='Percentile_Analysis', index=False)
    
    print(f"✓ Consolidated workbook created: {excel_file}")
    
    #####################################################
    # STEP 5: ENHANCE EXCEL WITH FORMATTING
    #####################################################
    print("\nSTEP 5: Enhancing Excel with formatting...")
    
    try:
        # Load the workbook for formatting
        wb = load_workbook(excel_file)
        
        # Define styles
        title_font = Font(name='Calibri', size=14, bold=True, color='FFFFFF')
        header_font = Font(name='Calibri', size=12, bold=True)
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        title_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
        border = Border(
            left=Side(style='thin'), 
            right=Side(style='thin'), 
            top=Side(style='thin'), 
            bottom=Side(style='thin')
        )
        
        # Format each sheet
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            
            # Add title row
            ws.insert_rows(1)
            ws.insert_rows(1)
            
            # Set title
            title_text = sheet_name.replace('_', ' ').title()
            ws['A1'] = f"Financial Analytics Report - {title_text}"
            ws['A1'].font = title_font
            ws['A1'].fill = title_fill
            ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
            
            # Merge title cells
            if ws.max_column > 1:
                ws.merge_cells(f'A1:{get_column_letter(ws.max_column)}1')
            
            # Format headers (row 3)
            if ws.max_row > 2:
                for cell in ws[3]:
                    if cell.value:
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.border = border
                        cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # Format data cells
            for row in ws.iter_rows(min_row=4, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
                for cell in row:
                    if cell.value is not None:
                        cell.border = border
                        cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # Auto-adjust column widths
            for col in range(1, ws.max_column + 1):
                column_letter = get_column_letter(col)
                max_length = 0
                
                for row in range(1, ws.max_row + 1):
                    cell_value = ws[f'{column_letter}{row}'].value
                    if cell_value:
                        max_length = max(max_length, len(str(cell_value)))
                
                # Set column width (with some padding)
                ws.column_dimensions[column_letter].width = min(max_length + 2, 50)
        
        # Save the formatted workbook
        wb.save(excel_file)
        print("✓ Excel formatting applied successfully")
        
    except Exception as e:
        print(f"⚠ Excel formatting failed: {str(e)}")
    
    #####################################################
    # STEP 6: CREATE SUMMARY VISUALIZATIONS
    #####################################################
    print("\nSTEP 6: Creating summary visualizations...")
    
    # Set up plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Create a comprehensive dashboard
    fig = plt.figure(figsize=(20, 16))
    
    # 1. Fund Performance by Category (Top subplot)
    if not fund_metrics.empty:
        ax1 = plt.subplot(3, 3, 1)
        category_performance = fund_metrics.groupby('Category')['Sharpe Ratio'].mean().sort_values(ascending=False)
        bars1 = ax1.bar(range(len(category_performance)), category_performance.values, 
                       color=sns.color_palette("viridis", len(category_performance)))
        ax1.set_title('Average Sharpe Ratio by Fund Category', fontweight='bold')
        ax1.set_xlabel('Fund Category')
        ax1.set_ylabel('Average Sharpe Ratio')
        ax1.set_xticks(range(len(category_performance)))
        ax1.set_xticklabels(category_performance.index, rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)
        
        # Add value labels
        for i, bar in enumerate(bars1):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.2f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. Portfolio Risk-Return Profile
    if not portfolio_metrics.empty:
        ax2 = plt.subplot(3, 3, 2)
        scatter = ax2.scatter(portfolio_metrics['Volatility']*100, portfolio_metrics['Expected Return']*100,
                            c=portfolio_metrics['Sharpe Ratio'], s=100, cmap='viridis', alpha=0.7)
        ax2.set_title('Portfolio Risk-Return Profile', fontweight='bold')
        ax2.set_xlabel('Volatility (%)')
        ax2.set_ylabel('Expected Return (%)')
        ax2.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax2)
        cbar.set_label('Sharpe Ratio')
        
        # Add portfolio labels
        for i, row in portfolio_metrics.iterrows():
            ax2.annotate(f"{row['Risk Profile'][:3]}-{row['Horizon'][:1]}", 
                        (row['Volatility']*100, row['Expected Return']*100),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    # 3. SIP Performance Comparison
    if not sip_summary.empty:
        ax3 = plt.subplot(3, 3, 3)
        sip_5y = sip_summary[sip_summary['Investment Period'] == '5 Years']
        if not sip_5y.empty:
            sip_5y_sorted = sip_5y.sort_values('Annualized Return %', ascending=True)
            portfolio_labels = sip_5y_sorted['Risk Profile'] + ' - ' + sip_5y_sorted['Horizon']
            
            bars3 = ax3.barh(range(len(sip_5y_sorted)), sip_5y_sorted['Annualized Return %'],
                           color=sns.color_palette("plasma", len(sip_5y_sorted)))
            ax3.set_title('SIP Returns (5 Years)', fontweight='bold')
            ax3.set_xlabel('Annualized Return (%)')
            ax3.set_yticks(range(len(sip_5y_sorted)))
            ax3.set_yticklabels(portfolio_labels)
            ax3.grid(True, alpha=0.3)
            
            # Add value labels
            for i, bar in enumerate(bars3):
                width = bar.get_width()
                ax3.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                        f'{width:.1f}%', ha='left', va='center', fontweight='bold')
    
    # 4. Monte Carlo Probability Analysis
    if not monte_carlo_summary.empty:
        ax4 = plt.subplot(3, 3, 4)
        mc_sorted = monte_carlo_summary.sort_values('Probability of ₹25L', ascending=True)
        portfolio_labels = mc_sorted['Risk Profile'] + ' - ' + mc_sorted['Horizon']
        
        bars4 = ax4.barh(range(len(mc_sorted)), mc_sorted['Probability of ₹25L'],
                       color=sns.color_palette("coolwarm", len(mc_sorted)))
        ax4.set_title('Probability of Reaching ₹25L Target', fontweight='bold')
        ax4.set_xlabel('Probability (%)')
        ax4.set_yticks(range(len(mc_sorted)))
        ax4.set_yticklabels(portfolio_labels)
        ax4.grid(True, alpha=0.3)
        
        # Add value labels
        for i, bar in enumerate(bars4):
            width = bar.get_width()
            ax4.text(width + 1, bar.get_y() + bar.get_height()/2.,
                    f'{width:.1f}%', ha='left', va='center', fontweight='bold')
    
    # 5. Rebalancing Frequency
    if not rebalancing_summary.empty:
        ax5 = plt.subplot(3, 3, 5)
        rebal_sorted = rebalancing_summary.sort_values('Rebalance Frequency', ascending=True)
        portfolio_labels = rebal_sorted['Risk Profile'] + ' - ' + rebal_sorted['Horizon']
        
        bars5 = ax5.barh(range(len(rebal_sorted)), rebal_sorted['Rebalance Frequency'],
                       color=sns.color_palette("Set2", len(rebal_sorted)))
        ax5.set_title('Rebalancing Frequency', fontweight='bold')
        ax5.set_xlabel('Frequency (% of periods)')
        ax5.set_yticks(range(len(rebal_sorted)))
        ax5.set_yticklabels(portfolio_labels)
        ax5.grid(True, alpha=0.3)
        
        # Add value labels
        for i, bar in enumerate(bars5):
            width = bar.get_width()
            ax5.text(width + 0.5, bar.get_y() + bar.get_height()/2.,
                    f'{width:.1f}%', ha='left', va='center', fontweight='bold')
    
    # 6. Top Performing Funds
    if not fund_metrics.empty:
        ax6 = plt.subplot(3, 3, 6)
        top_funds = fund_metrics.nlargest(8, 'Sharpe Ratio')
        
        bars6 = ax6.bar(range(len(top_funds)), top_funds['Sharpe Ratio'],
                       color=sns.color_palette("tab10", len(top_funds)))
        ax6.set_title('Top 8 Funds by Sharpe Ratio', fontweight='bold')
        ax6.set_xlabel('Fund Rank')
        ax6.set_ylabel('Sharpe Ratio')
        ax6.set_xticks(range(len(top_funds)))
        ax6.set_xticklabels([f"#{i+1}" for i in range(len(top_funds))])
        ax6.grid(True, alpha=0.3)
        
        # Add value labels
        for i, bar in enumerate(bars6):
            height = bar.get_height()
            ax6.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.2f}', ha='center', va='bottom', fontweight='bold')
    
    # 7-9. Summary Statistics
    ax7 = plt.subplot(3, 3, (7, 9))
    ax7.axis('off')
    
    # Create summary text
    summary_text = "FINANCIAL ANALYTICS SUMMARY\n\n"
    
    if not fund_metrics.empty:
        summary_text += f"📊 FUND ANALYSIS:\n"
        summary_text += f"• Total Funds Analyzed: {len(fund_metrics)}\n"
        summary_text += f"• Best Sharpe Ratio: {fund_metrics['Sharpe Ratio'].max():.3f}\n"
        summary_text += f"• Average Return: {fund_metrics['1Y CAGR'].mean()*100:.2f}%\n\n"
    
    if not portfolio_metrics.empty:
        summary_text += f"🎯 PORTFOLIO ANALYSIS:\n"
        summary_text += f"• Portfolio Templates: {len(portfolio_metrics)}\n"
        summary_text += f"• Best Expected Return: {portfolio_metrics['Expected Return'].max()*100:.2f}%\n"
        summary_text += f"• Best Sharpe Ratio: {portfolio_metrics['Sharpe Ratio'].max():.3f}\n\n"
    
    if not monte_carlo_summary.empty:
        summary_text += f"🎲 MONTE CARLO INSIGHTS:\n"
        summary_text += f"• Simulations per Portfolio: 10,000\n"
        summary_text += f"• Best Probability (₹25L): {monte_carlo_summary['Probability of ₹25L'].max():.1f}%\n"
        summary_text += f"• Investment Horizon: 10 Years\n\n"
    
    if not rebalancing_summary.empty:
        summary_text += f"🔄 REBALANCING INSIGHTS:\n"
        summary_text += f"• Average Frequency: {rebalancing_summary['Rebalance Frequency'].mean():.1f}%\n"
        summary_text += f"• Monitoring: Every 6 months\n"
        summary_text += f"• Threshold: 5% drift\n\n"
    
    summary_text += f"💡 KEY RECOMMENDATIONS:\n"
    summary_text += f"• Conservative: Debt-focused for stability\n"
    summary_text += f"• Balanced: Mixed allocation for growth\n"
    summary_text += f"• Aggressive: Equity-heavy for wealth creation\n"
    summary_text += f"• SIP: ₹10,000 monthly recommended\n"
    summary_text += f"• Rebalance: Monitor every 6 months"
    
    ax7.text(0.05, 0.95, summary_text, transform=ax7.transAxes, fontsize=11,
            verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.suptitle('COMPREHENSIVE FINANCIAL ANALYTICS DASHBOARD', fontsize=20, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.subplots_adjust(top=0.95)
    plt.savefig('Financial_Analytics_Dashboard.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ Comprehensive dashboard created: 'Financial_Analytics_Dashboard.png'")
    
    #####################################################
    # STEP 7: GENERATE FINAL SUMMARY REPORT
    #####################################################
    print("\nSTEP 7: Generating final summary report...")
    
    print("\n" + "="*80)
    print("FINANCIAL ANALYTICS ENGINE - COMPLETE ANALYSIS SUMMARY")
    print("="*80)
    
    print(f"\n📊 ANALYSIS COMPLETED:")
    print(f"✓ Part 1: Risk & Return Metrics for {len(fund_metrics) if not fund_metrics.empty else 0} funds")
    print(f"✓ Part 2: {len(portfolio_metrics) if not portfolio_metrics.empty else 0} Portfolio Templates (Conservative/Balanced/Aggressive × Short/Long)")
    print(f"✓ Part 3: SIP Simulation for {len(sip_summary) if not sip_summary.empty else 0} scenarios")
    print(f"✓ Part 4: Monte Carlo Analysis (10,000 simulations per portfolio)")
    print(f"✓ Part 5: Rebalancing Trigger Module (6-month monitoring)")
    print(f"✓ Part 6: Comprehensive Export & Consolidation")
    
    print(f"\n📁 FILES GENERATED:")
    print(f"• Financial_Analytics_Complete_Report.xlsx - Main consolidated report")
    print(f"• Financial_Analytics_Dashboard.png - Visual summary dashboard")
    print(f"• Part1_Fund_Metrics.xlsx - Detailed fund analysis")
    print(f"• Part2_Portfolio_Templates.xlsx - Portfolio allocations")
    print(f"• Part3_SIP_Simulation.xlsx - SIP performance analysis")
    print(f"• Part4_Monte_Carlo_Simulation.xlsx - Risk scenario analysis")
    print(f"• Part5_Rebalancing_Analysis.xlsx - Rebalancing recommendations")
    print(f"• Various visualization charts (PNG files)")
    
    if not recommendations_df.empty:
        print(f"\n🎯 KEY RECOMMENDATIONS:")
        for _, rec in recommendations_df.iterrows():
            print(f"\n{rec['Risk Profile'].upper()} INVESTORS:")
            print(f"  • Strategy: {rec['Recommendation']}")
            print(f"  • Expected Return: {rec['Expected Return']}")
            print(f"  • Risk Level: {rec['Risk Level']}")
            print(f"  • Best For: {rec['Suitable For']}")
            print(f"  • Key Benefit: {rec['Key Benefit']}")
    
    print(f"\n💡 IMPLEMENTATION GUIDELINES:")
    print(f"• Start with ₹10,000 monthly SIP")
    print(f"• Choose portfolio based on risk tolerance and time horizon")
    print(f"• Monitor and rebalance every 6 months")
    print(f"• Maintain discipline during market volatility")
    print(f"• Review and adjust strategy annually")
    
    print(f"\n🎉 ANALYSIS COMPLETE!")
    print(f"All results have been consolidated into the main Excel report.")
    print(f"Use the dashboard and detailed sheets for investment decisions.")
    
    print("\n" + "="*80)
    
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
